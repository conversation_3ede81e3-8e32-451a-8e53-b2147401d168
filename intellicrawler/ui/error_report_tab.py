"""
Error Report Tab - Provides UI for viewing and exporting error logs
"""

import os
import sys
import json
import time
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                           QTextEdit, QFileDialog, QTableWidget, QTableWidgetItem, 
                           QHeaderView, QSplitter, QGroupBox, QMessageBox, QComboBox)
from PyQt5.QtCore import Qt, QSize
from intellicrawler.utils.logger import get_logger
from intellicrawler.utils.error_handler import ErrorHandler

class ErrorReportTab(QWidget):
    """UI tab for viewing and managing error reports"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.logger = get_logger()
        
        # Set up the UI
        self.init_ui()
        
        # Load errors on initialization
        self.refresh_errors()
    
    def init_ui(self):
        """Set up the user interface"""
        main_layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("Error Reports and Logs")
        header_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        main_layout.addWidget(header_label)
        
        # Description
        description = QLabel(
            "This tab shows error reports and logs for debugging purposes. "
            "You can view error details, refresh the list, and export error reports."
        )
        description.setWordWrap(True)
        main_layout.addWidget(description)
        
        # Main splitter
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter, 1)
        
        # Error list group
        error_list_group = QGroupBox("Error List")
        error_list_layout = QVBoxLayout(error_list_group)
        
        # Error table
        self.error_table = QTableWidget()
        self.error_table.setColumnCount(4)
        self.error_table.setHorizontalHeaderLabels(["Error ID", "Timestamp", "Type", "Message"])
        self.error_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.error_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.error_table.setSelectionMode(QTableWidget.SingleSelection)
        self.error_table.itemSelectionChanged.connect(self.on_error_selected)
        error_list_layout.addWidget(self.error_table)
        
        # Table controls
        table_controls = QHBoxLayout()
        
        self.refresh_button = QPushButton("Refresh Error List")
        self.refresh_button.clicked.connect(self.refresh_errors)
        table_controls.addWidget(self.refresh_button)
        
        self.clear_selection_button = QPushButton("Clear Selection")
        self.clear_selection_button.clicked.connect(self.clear_selection)
        table_controls.addWidget(self.clear_selection_button)
        
        error_list_layout.addLayout(table_controls)
        
        # Error details group
        error_details_group = QGroupBox("Error Details")
        error_details_layout = QVBoxLayout(error_details_group)
        
        # Error details text area
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setPlaceholderText("Select an error from the list above to view its details.")
        error_details_layout.addWidget(self.details_text)
        
        # Add groups to splitter
        splitter.addWidget(error_list_group)
        splitter.addWidget(error_details_group)
        splitter.setSizes([200, 400])
        
        # Export controls
        export_group = QGroupBox("Export Controls")
        export_layout = QHBoxLayout(export_group)
        
        # Log type selector
        self.log_type_label = QLabel("Log Type:")
        export_layout.addWidget(self.log_type_label)
        
        self.log_type_selector = QComboBox()
        self.log_type_selector.addItems([
            "Session Errors", 
            "Error Log File", 
            "Application Log File"
        ])
        export_layout.addWidget(self.log_type_selector)
        
        # Export button
        self.export_button = QPushButton("Export Error Report")
        self.export_button.clicked.connect(self.export_errors)
        export_layout.addWidget(self.export_button)
        
        # Open logs folder button
        self.open_logs_button = QPushButton("Open Logs Folder")
        self.open_logs_button.clicked.connect(self.open_logs_folder)
        export_layout.addWidget(self.open_logs_button)
        
        main_layout.addWidget(export_group)
        
        # Set layout
        self.setLayout(main_layout)
    
    def refresh_errors(self):
        """Refresh the error list from the session errors"""
        # Get errors from ErrorHandler
        errors = ErrorHandler.get_session_errors()
        
        # Clear the table
        self.error_table.setRowCount(0)
        
        # Add errors to the table
        for i, error in enumerate(errors):
            self.error_table.insertRow(i)
            
            # Add error data
            self.error_table.setItem(i, 0, QTableWidgetItem(error.get('error_id', 'Unknown')))
            
            # Format timestamp
            timestamp = error.get('timestamp', '')
            try:
                dt = datetime.fromisoformat(timestamp)
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                formatted_time = timestamp
                
            self.error_table.setItem(i, 1, QTableWidgetItem(formatted_time))
            self.error_table.setItem(i, 2, QTableWidgetItem(error.get('type', 'Unknown')))
            self.error_table.setItem(i, 3, QTableWidgetItem(error.get('message', 'No message')))
        
        # Resize columns to content
        self.error_table.resizeColumnsToContents()
        
        # Log the refresh
        self.logger.debug(f"Refreshed error list - {len(errors)} errors loaded")
    
    def on_error_selected(self):
        """Show details for the selected error"""
        selected = self.error_table.selectedItems()
        if not selected:
            self.details_text.clear()
            return
        
        # Get the error ID from the first column
        row = selected[0].row()
        error_id = self.error_table.item(row, 0).text()
        
        # Find the error in the session errors
        errors = ErrorHandler.get_session_errors()
        found_error = None
        
        for error in errors:
            if error.get('error_id') == error_id:
                found_error = error
                break
        
        if not found_error:
            self.details_text.setPlainText(f"Error with ID {error_id} not found.")
            return
        
        # Format and display the error details
        details = f"ERROR ID: {error_id}\n"
        details += f"TIMESTAMP: {found_error.get('timestamp', 'Unknown')}\n"
        details += f"TYPE: {found_error.get('type', 'Unknown')}\n"
        details += f"MESSAGE: {found_error.get('message', 'No message')}\n\n"
        details += "TRACEBACK:\n"
        details += found_error.get('traceback', 'No traceback available')
        
        # Set the details text
        self.details_text.setPlainText(details)
    
    def clear_selection(self):
        """Clear the current selection"""
        self.error_table.clearSelection()
        self.details_text.clear()
    
    def export_errors(self):
        """Export error reports to a file"""
        try:
            # Get the selected log type
            log_type = self.log_type_selector.currentText()
            
            if log_type == "Session Errors":
                # Get a save file location
                file_path, _ = QFileDialog.getSaveFileName(
                    self, "Export Error Report", "", 
                    "JSON Files (*.json);;All Files (*)"
                )
                
                if not file_path:
                    return
                
                # Export the report
                exported_path = ErrorHandler.export_session_errors(file_path)
                
                if exported_path:
                    QMessageBox.information(self, "Export Successful", 
                                           f"Error report exported to:\n{exported_path}")
                else:
                    QMessageBox.warning(self, "Export Failed", 
                                       "Failed to export error report. See log for details.")
            
            elif log_type == "Error Log File":
                # Get the error log file
                error_log_dir = os.path.expanduser("~/.supercrawler/logs/errors")
                error_log_file = os.path.join(error_log_dir, "supercrawler_errors.log")
                
                # Define target file
                file_path, _ = QFileDialog.getSaveFileName(
                    self, "Export Error Log", "", 
                    "Log Files (*.log);;Text Files (*.txt);;All Files (*)"
                )
                
                if not file_path:
                    return
                
                # Copy the log file
                if os.path.exists(error_log_file):
                    with open(error_log_file, 'r', encoding='utf-8') as src:
                        with open(file_path, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    
                    QMessageBox.information(self, "Export Successful", 
                                          f"Error log exported to:\n{file_path}")
                else:
                    QMessageBox.warning(self, "Export Failed", 
                                      f"Error log file not found:\n{error_log_file}")
            
            elif log_type == "Application Log File":
                # Get the application log file
                log_dir = os.path.expanduser("~/.supercrawler/logs")
                timestamp = datetime.now().strftime("%Y%m%d")
                log_file = os.path.join(log_dir, f"supercrawler_{timestamp}.log")
                
                # Define target file
                file_path, _ = QFileDialog.getSaveFileName(
                    self, "Export Application Log", "", 
                    "Log Files (*.log);;Text Files (*.txt);;All Files (*)"
                )
                
                if not file_path:
                    return
                
                # Copy the log file
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as src:
                        with open(file_path, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    
                    QMessageBox.information(self, "Export Successful", 
                                          f"Application log exported to:\n{file_path}")
                else:
                    QMessageBox.warning(self, "Export Failed", 
                                      f"Application log file not found:\n{log_file}")
        
        except Exception as e:
            self.logger.error(f"Error exporting logs: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Export Error", 
                              f"An error occurred while exporting:\n{str(e)}")
    
    def open_logs_folder(self):
        """Open the logs folder in the system file explorer"""
        try:
            log_dir = os.path.expanduser("~/.supercrawler/logs")
            
            if sys.platform == 'win32':
                os.startfile(log_dir)
            elif sys.platform == 'darwin':  # macOS
                import subprocess
                subprocess.run(['open', log_dir])
            else:  # Linux
                import subprocess
                subprocess.run(['xdg-open', log_dir])
                
            self.logger.info(f"Opened logs folder: {log_dir}")
        except Exception as e:
            self.logger.error(f"Error opening logs folder: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Error", 
                              f"Failed to open logs folder:\n{str(e)}") 