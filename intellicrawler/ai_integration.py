import os
import json
import openai
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal, QThread
from intellicrawler.utils.logger import get_logger, log_error, exception_handler
from intellicrawler.utils.config import load_config, save_config

class DeepSeekAI(QObject):
    """
    Integration with DeepSeek AI API for analysis and context
    """
    
    # Signals
    status_updated = pyqtSignal(str)
    analysis_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    agent_progress = pyqtSignal(str, int)  # message, progress percentage
    agent_completed = pyqtSignal(list)  # list of scraped data
    
    # New signals for intelligent crawling
    context_detected = pyqtSignal(dict)  # detected context information
    crawl_strategy_decided = pyqtSignal(str)  # AI decision about crawl strategy
    page_quality_assessed = pyqtSignal(str, int)  # url, quality_score
    intelligent_decision = pyqtSignal(str)  # AI decision explanation
    
    # Available models (updated as of January 2025)
    AVAILABLE_MODELS = {
        "deepseek-reasoner": {
            "description": "DeepSeek R1 - Advanced reasoning model with enhanced problem-solving",
            "context_window": 128000,
            "best_for": ["Complex analysis", "Mathematical reasoning", "Research", "Detailed problem solving"],
            "latest_update": "DeepSeek-R1-0528"
        },
        "deepseek-chat": {
            "description": "DeepSeek V3 - Upgraded general-purpose model with enhanced capabilities",
            "context_window": 128000,  # Updated context window for V3
            "best_for": ["General tasks", "Content generation", "Code generation", "Summarization"],
            "latest_update": "DeepSeek-V3 (upgraded from V2.5-1210)"
        }
    }
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        self.config = load_config()
        
        # Configure OpenAI client for DeepSeek API
        self.api_key = self.config.get("deepseek_api_key", "")
        self.base_url = "https://api.deepseek.com"  # Using official DeepSeek API endpoint
        self.client = None  # Initialize client to None
        
        # Agent state
        self.agent_active = False
        self.agent_thread = None
        
        # Set up client if API key is available
        if self.api_key:
            self._setup_client()
    
    def _setup_client(self):
        """Set up the OpenAI client with DeepSeek API configuration"""
        try:
            if not self.api_key:
                self.logger.warning("DeepSeek API key is not set")
                self.error_occurred.emit("API key is not set. Please go to Settings to configure your DeepSeek API key.")
                return False
                
            # Log API key state (masked for security)
            masked_key = "**********" + self.api_key[-4:] if len(self.api_key) > 4 else "********"
            self.logger.info(f"Setting up API client with key: {masked_key}")
            
            try:
                # Import OpenAI client properly and configure with base_url
                from openai import OpenAI
                self.client = OpenAI(
                    api_key=self.api_key,
                    base_url="https://api.deepseek.com"
                )
                
                # For backward compatibility with functions that might use the global client
                openai.api_key = self.api_key
                openai.base_url = self.base_url
                
                self.logger.info("DeepSeek API client configured")
                return True
            except ImportError:
                # Fallback for older versions of OpenAI package
                self.logger.warning("Using legacy OpenAI client initialization")
                openai.api_key = self.api_key
                openai.base_url = self.base_url
                # Use the same pattern with explicit parameters for compatibility
                self.client = openai.Client(
                    api_key=self.api_key,
                    base_url=self.base_url
                )
                return True
                
        except Exception as e:
            error_id = log_error(e, "Failed to configure DeepSeek API client")
            self.error_occurred.emit(f"API configuration error [{error_id}]: {str(e)}")
            return False
    
    def set_api_key(self, api_key):
        """Set the DeepSeek API key"""
        self.logger.info(f"Setting new API key (length: {len(api_key) if api_key else 0})")
        self.api_key = api_key
        self.config["deepseek_api_key"] = api_key
        
        # Ensure config directory exists
        config_dir = os.path.expanduser("~/.supercrawler")
        os.makedirs(config_dir, exist_ok=True)
        
        # Save configuration
        success = save_config(self.config)
        if success:
            self.logger.info("Config saved successfully with new API key")
        else:
            self.logger.error("Failed to save config with new API key")
            
        if api_key:
            success = self._setup_client()
            if success:
                self.status_updated.emit("API key updated successfully")
            else:
                self.status_updated.emit("API key was saved but there was an error setting up the API client")
        else:
            self.status_updated.emit("API key cleared")
    
    def check_api_key(self):
        """Check if API key is configured and valid"""
        # Force reload config in case it was updated
        try:
            self.config = load_config()
            fresh_api_key = self.config.get("deepseek_api_key", "")
            
            # If we have a new key from config but not in memory, update it
            if fresh_api_key and fresh_api_key != self.api_key:
                self.logger.info("Found updated API key in config, updating instance")
                self.api_key = fresh_api_key
                self._setup_client()
            elif fresh_api_key and not self.api_key:
                self.logger.info("Found API key in config but not in memory, updating")
                self.api_key = fresh_api_key
                self._setup_client()
                
            # Log key state for debugging
            if self.api_key:
                self.logger.info(f"API key found (length: {len(self.api_key)})")
            else:
                self.logger.warning("No API key found after reload attempt")
                
            if not self.api_key:
                error_msg = "API key for DeepSeek is not configured. Please set it in the Settings tab."
                self.logger.error(error_msg)
                return False
                
            return True
        except Exception as e:
            error_msg = f"Error checking API key: {str(e)}"
            self.logger.error(error_msg)
            return False
    
    @exception_handler
    def analyze_content(self, content, model="deepseek-reasoner", analysis_type="summary", temperature=0.2):
        """
        Analyze content using DeepSeek AI with intelligent chunking
        
        Args:
            content (str or list): Content to analyze (can be string or list of pages)
            model (str): DeepSeek model to use (deepseek-reasoner for R1, deepseek-chat for V3)
            analysis_type (str): Type of analysis to perform
            temperature (float): Temperature for generation (0.0-1.0)
        
        Returns:
            dict: Analysis results with content and metadata
        """
        if not self.api_key:
            self.error_occurred.emit("DeepSeek API key not set. Please configure in settings.")
            return None
            
        # Ensure client is initialized
        if not self.client:
            success = self._setup_client()
            if not success:
                self.error_occurred.emit("Failed to initialize API client. Please check your API key and connection.")
                return None
        
        try:
            from intellicrawler.utils.text_processor import ContentProcessor, ChunkingStrategy
            
            self.status_updated.emit(f"Analyzing content using DeepSeek {model}...")
            
            # Initialize text processor with model-specific limits
            processor = ContentProcessor(model=model)
            
            # Handle different content types
            if isinstance(content, list):
                # List of page data - use intelligent chunking
                chunks = processor.process_crawled_data(
                    content, 
                    strategy=ChunkingStrategy.SEMANTIC,
                    preserve_context=True
                )
                
                if len(chunks) == 1:
                    # Single chunk - process normally
                    chunk_content = chunks[0].content
                    return self._analyze_single_content(chunk_content, model, analysis_type, temperature)
                else:
                    # Multiple chunks - process iteratively
                    return self._analyze_chunked_content(chunks, model, analysis_type, temperature, processor)
            else:
                # String content - check if chunking needed
                content_str = str(content)
                if len(content_str) <= processor.limits["max_chars"]:
                    # Small enough for single analysis
                    return self._analyze_single_content(content_str, model, analysis_type, temperature)
                else:
                    # Need to chunk string content
                    # Convert to list format for processing
                    mock_data = [{"title": "Content", "url": "N/A", "content": content_str}]
                    chunks = processor.process_crawled_data(mock_data, strategy=ChunkingStrategy.SEMANTIC)
                    return self._analyze_chunked_content(chunks, model, analysis_type, temperature, processor)
                    
        except Exception as e:
            error_id = log_error(e, "Error during content analysis")
            self.error_occurred.emit(f"Analysis error [{error_id}]: {str(e)}")
            return None
    
    def _analyze_single_content(self, content, model, analysis_type, temperature):
        """Analyze a single piece of content that fits within model limits"""
        # Special handling for document enhancement
        if analysis_type == "document_enhancement":
            return self._enhance_documentation(content, model, temperature)
        
        # Prepare prompt based on analysis type
        if analysis_type == "summary":
            prompt = f"Please provide a concise summary of the following content:\n\n{content}"
        elif analysis_type == "key_points":
            prompt = f"Extract and list the key points from the following content:\n\n{content}"
        elif analysis_type == "sentiment":
            prompt = f"Analyze the sentiment of the following content. Rate it on a scale from -5 (very negative) to +5 (very positive) and explain your rating:\n\n{content}"
        elif analysis_type == "entities":
            prompt = f"Extract all named entities (people, organizations, locations, dates, etc.) from the following content:\n\n{content}"
        elif analysis_type == "json":
            prompt = f"""
            Extract structured information from the following content and return it in a clean JSON format.
            Include key entities, facts, and relationships found in the text.
            Format the JSON to be well-structured and easy to parse programmatically.
            
            Content:
            {content}
            """
        else:
            prompt = f"Analyze the following content and provide detailed insights:\n\n{content}"
        
        # Call DeepSeek API with proper error handling
        self.logger.info(f"Calling DeepSeek API with model: {model}, analysis_type: {analysis_type}")
        
        messages = [
            {"role": "system", "content": "You are an expert analyst that specializes in extracting insights from web content."},
            {"role": "user", "content": prompt}
        ]
        
        try:
            # Using the client instance directly
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=4000
            )
            
            # Extract the response text
            response_text = response.choices[0].message.content
            
            # Log success
            self.logger.info(f"Successfully received analysis from DeepSeek API: {len(response_text)} characters")
            
            # Prepare result dictionary
            result = {
                "analysis_type": analysis_type,
                "model": model,
                "content": response_text,
                "timestamp": datetime.now().isoformat()
            }
            
            # Emit signal with results
            self.analysis_completed.emit(result)
            self.status_updated.emit("Content analysis completed")
            
            return result
            
        except AttributeError:
            # Fallback for older OpenAI SDK versions
            self.logger.warning("Using legacy OpenAI API call pattern")
            response = openai.ChatCompletion.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=4000
            )
            
            # Extract the response text
            response_text = response.choices[0].message.content
            
            # Prepare result dictionary
            result = {
                "analysis_type": analysis_type,
                "model": model,
                "content": response_text,
                "timestamp": datetime.now().isoformat()
            }
            
            # Emit signal with results
            self.analysis_completed.emit(result)
            self.status_updated.emit("Content analysis completed")

            return result

        except Exception as e:
            error_id = log_error(e, "Error in single content analysis")
            error_msg = f"Analysis failed [{error_id}]: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return None
    
    def _analyze_chunked_content(self, chunks, model, analysis_type, temperature, processor):
        """Analyze content that has been split into multiple chunks"""
        self.logger.info(f"Processing {len(chunks)} chunks for analysis")
        
        chunk_results = []
        total_chunks = len(chunks)
        
        # Analyze each chunk
        for i, chunk in enumerate(chunks):
            self.status_updated.emit(f"Analyzing chunk {i+1} of {total_chunks}...")
            
            try:
                # Analyze this chunk
                chunk_result = self._analyze_single_content(
                    chunk.content, model, analysis_type, temperature
                )
                
                if chunk_result:
                    chunk_results.append({
                        "chunk_id": chunk.metadata.chunk_id,
                        "source_page": chunk.metadata.source_page,
                        "content": chunk_result["content"],
                        "chunk_metadata": {
                            "chunk_type": chunk.metadata.chunk_type,
                            "word_count": chunk.metadata.word_count,
                            "char_count": chunk.metadata.char_count
                        }
                    })
                else:
                    self.logger.warning(f"Failed to analyze chunk {i+1}")
                    
            except Exception as e:
                self.logger.error(f"Error analyzing chunk {i+1}: {str(e)}")
                continue
        
        if not chunk_results:
            raise Exception("Failed to analyze any chunks")
        
        # Combine results using different strategies based on analysis type
        if analysis_type == "summary":
            combined_result = self._combine_summaries(chunk_results, model, temperature)
        elif analysis_type == "key_points":
            combined_result = self._combine_key_points(chunk_results)
        elif analysis_type == "entities":
            combined_result = self._combine_entities(chunk_results)
        else:
            combined_result = self._combine_general_analysis(chunk_results, analysis_type)
        
        # Get processing summary
        processing_summary = processor.get_processing_summary(chunks)
        
        # Prepare final result
        final_result = {
            "analysis_type": analysis_type,
            "model": model,
            "content": combined_result,
            "timestamp": datetime.now().isoformat(),
            "chunking_info": {
                "total_chunks": len(chunks),
                "processing_strategy": "semantic_chunking",
                "processing_summary": processing_summary
            },
            "chunk_details": chunk_results
        }
        
        # Emit signal with results
        self.analysis_completed.emit(final_result)
        self.status_updated.emit(f"Content analysis completed ({len(chunks)} chunks processed)")
        
        return final_result
    
    def _combine_summaries(self, chunk_results, model, temperature):
        """Combine multiple chunk summaries into a coherent final summary"""
        chunk_summaries = [result["content"] for result in chunk_results]
        
        # Create a meta-summary from the chunk summaries
        combined_text = "\n\n".join([
            f"Summary {i+1}:\n{summary}" 
            for i, summary in enumerate(chunk_summaries)
        ])
        
        meta_prompt = f"""
        The following are summaries of different sections of a larger document. 
        Please create a comprehensive, coherent summary that combines these individual summaries:
        
        {combined_text}
        
        Provide a well-structured summary that captures the main themes, key points, and important insights from all sections.
        """
        
        try:
            # Generate meta-summary
            messages = [
                {"role": "system", "content": "You are an expert at synthesizing information from multiple sources into coherent summaries."},
                {"role": "user", "content": meta_prompt}
            ]
            
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=3000
            )
            
            meta_summary = response.choices[0].message.content
            
            return f"{meta_summary}\n\n---\n\nIndividual Section Summaries:\n\n" + "\n\n".join([
                f"Section {i+1} ({result['source_page']}):\n{result['content']}"
                for i, result in enumerate(chunk_results)
            ])
            
        except Exception as e:
            self.logger.warning(f"Failed to create meta-summary, returning concatenated summaries: {str(e)}")
            return "\n\n".join([
                f"Section {i+1} ({result['source_page']}):\n{result['content']}"
                for i, result in enumerate(chunk_results)
            ])
    
    def _combine_key_points(self, chunk_results):
        """Combine key points from multiple chunks"""
        all_points = []
        
        for i, result in enumerate(chunk_results):
            all_points.append(f"From {result['source_page']}:")
            all_points.append(result["content"])
            all_points.append("")  # Empty line separator
        
        return "\n".join(all_points)
    
    def _combine_entities(self, chunk_results):
        """Combine extracted entities from multiple chunks"""
        all_entities = []
        
        for i, result in enumerate(chunk_results):
            all_entities.append(f"Entities from {result['source_page']}:")
            all_entities.append(result["content"])
            all_entities.append("")  # Empty line separator
        
        return "\n".join(all_entities)
    
    def _combine_general_analysis(self, chunk_results, analysis_type):
        """Combine general analysis results from multiple chunks"""
        combined_content = []
        
        for i, result in enumerate(chunk_results):
            combined_content.append(f"Analysis of {result['source_page']} ({analysis_type}):")
            combined_content.append(result["content"])
            combined_content.append("-" * 50)  # Separator
        
        return "\n\n".join(combined_content)
    
    def _enhance_documentation(self, content, model, temperature):
        """
        Enhance documentation by extracting URLs, scraping them, and generating comprehensive content
        
        Args:
            content (str): Original documentation content
            model (str): DeepSeek model to use
            temperature (float): Temperature for generation
            
        Returns:
            dict: Enhanced documentation result
        """
        try:
            self.status_updated.emit("🔍 Analyzing documentation for enhancement opportunities...")
            
            # Step 1: Extract URLs from the content
            urls = self._extract_urls_from_content(content)
            self.logger.info(f"Extracted {len(urls)} URLs from documentation")
            
            if not urls:
                self.status_updated.emit("❌ No URLs found in documentation to enhance")
                return {
                    "analysis_type": "document_enhancement",
                    "model": model,
                    "content": "No URLs were found in the documentation to enhance. The document appears to be self-contained.",
                    "timestamp": datetime.now().isoformat(),
                    "enhancement_status": "no_urls_found"
                }
            
            # Step 2: Extract main topics from the original content for web search
            topics = self._extract_main_topics(content, model, temperature)
            
            # Step 3: Scrape the extracted URLs
            self.status_updated.emit(f"🕷️ Scraping {len(urls)} referenced URLs...")
            scraped_data = self._scrape_reference_urls(urls)
            
            # Step 4: Perform web searches on main topics
            self.status_updated.emit("🌐 Performing web searches on key topics...")
            search_data = self._perform_web_searches(topics)
            
            # Step 5: Generate enhanced documentation
            self.status_updated.emit("✨ Generating enhanced documentation with AI...")
            enhanced_content = self._generate_enhanced_documentation(
                content, scraped_data, search_data, topics, model, temperature
            )
            
            # Prepare final result
            result = {
                "analysis_type": "document_enhancement",
                "model": model,
                "content": enhanced_content,
                "timestamp": datetime.now().isoformat(),
                "enhancement_status": "success",
                "enhancement_details": {
                    "urls_processed": len(urls),
                    "pages_scraped": len(scraped_data),
                    "topics_researched": len(topics),
                    "search_queries_made": len(search_data)
                },
                "source_urls": urls,
                "topics_researched": topics
            }
            
            self.status_updated.emit("✅ Document enhancement completed successfully")
            return result
            
        except Exception as e:
            log_error(e, "Error during document enhancement")
            self.status_updated.emit(f"❌ Document enhancement failed: {str(e)}")
            return {
                "analysis_type": "document_enhancement",
                "model": model,
                "content": f"Document enhancement failed due to an error: {str(e)}",
                "timestamp": datetime.now().isoformat(),
                "enhancement_status": "error",
                "error": str(e)
            }
    
    def _extract_urls_from_content(self, content):
        """Extract URLs from markdown content, especially from Source References section"""
        import re
        
        urls = []
        
        # Pattern for markdown links [text](url)
        markdown_pattern = r'\[([^\]]*)\]\((https?://[^\)]+)\)'
        markdown_matches = re.findall(markdown_pattern, content)
        for text, url in markdown_matches:
            urls.append(url.strip())
        
        # Pattern for plain URLs
        url_pattern = r'https?://[^\s\)\]\}]+'
        plain_urls = re.findall(url_pattern, content)
        urls.extend([url.strip() for url in plain_urls])
        
        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)
        
        self.logger.info(f"Extracted URLs: {unique_urls}")
        return unique_urls
    
    def _extract_main_topics(self, content, model, temperature):
        """Extract main topics from the content for web search"""
        try:
            prompt = f"""
            Analyze the following documentation and extract 3-5 main topics that would benefit from additional research.
            Return only the topic keywords/phrases, one per line, without explanations.
            Focus on technical concepts, technologies, or subjects that could have recent developments or additional information available online.
            
            Documentation:
            {content[:5000]}...
            """
            
            messages = [
                {"role": "system", "content": "You are an expert researcher who identifies key topics for further investigation."},
                {"role": "user", "content": prompt}
            ]
            
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=500
            )
            
            topics_text = response.choices[0].message.content
            topics = [topic.strip() for topic in topics_text.split('\n') if topic.strip()]
            
            self.logger.info(f"Extracted topics for research: {topics}")
            return topics[:5]  # Limit to 5 topics
            
        except Exception as e:
            self.logger.error(f"Failed to extract topics: {str(e)}")
            return ["general technology topics", "software development"]
    
    def _scrape_reference_urls(self, urls):
        """Scrape the referenced URLs using IntelliCrawler"""
        scraped_data = []
        
        try:
            # Import crawler here to avoid circular imports
            from intellicrawler.crawler import Crawler
            
            # Create a temporary crawler instance
            crawler = Crawler()
            
            for i, url in enumerate(urls[:10]):  # Limit to 10 URLs to avoid overwhelming
                try:
                    self.status_updated.emit(f"🕷️ Scraping URL {i+1}/{min(len(urls), 10)}: {url}")
                    
                    # Scrape single page
                    page_data = crawler.scrape_page(url)
                    if page_data and page_data.get('content'):
                        scraped_data.append({
                            "url": url,
                            "title": page_data.get('title', 'No title'),
                            "content": page_data.get('content', '')[:5000],  # Limit content
                            "scraped_at": datetime.now().isoformat()
                        })
                        self.logger.info(f"Successfully scraped: {url}")
                    else:
                        self.logger.warning(f"Failed to scrape content from: {url}")
                        
                except Exception as e:
                    self.logger.error(f"Error scraping {url}: {str(e)}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Error setting up crawler for URL scraping: {str(e)}")
        
        self.logger.info(f"Successfully scraped {len(scraped_data)} pages")
        return scraped_data
    
    def _perform_web_searches(self, topics):
        """Perform web searches on topics (placeholder - can be enhanced with actual search API)"""
        search_data = []
        
        for topic in topics:
            try:
                self.status_updated.emit(f"🔍 Researching topic: {topic}")
                
                # This is a placeholder for web search functionality
                # In a real implementation, you would use a search API like:
                # - Google Custom Search API
                # - Bing Search API
                # - DuckDuckGo API
                # - SerpAPI
                
                search_result = {
                    "topic": topic,
                    "search_query": f"{topic} latest developments 2025",
                    "status": "placeholder",
                    "note": "Web search functionality would be implemented here with a real search API"
                }
                
                search_data.append(search_result)
                
            except Exception as e:
                self.logger.error(f"Error searching for topic {topic}: {str(e)}")
                continue
        
        return search_data
    
    def _generate_enhanced_documentation(self, original_content, scraped_data, search_data, topics, model, temperature):
        """Generate enhanced documentation using original content, scraped data, and search results"""
        try:
            # Prepare context from scraped data
            scraped_context = ""
            if scraped_data:
                scraped_context = "\n\n".join([
                    f"**{data['title']}** ({data['url']}):\n{data['content'][:2000]}"
                    for data in scraped_data[:5]  # Limit to top 5 scraped pages
                ])
            
            # Create comprehensive prompt for enhancement
            prompt = f"""
            You are an expert technical documentation specialist. I need you to create an enhanced, more comprehensive version of the following documentation.
            
            ORIGINAL DOCUMENTATION:
            {original_content}
            
            ADDITIONAL CONTEXT FROM REFERENCED SOURCES:
            {scraped_context if scraped_context else "No additional sources were successfully scraped."}
            
            KEY TOPICS FOR ENHANCEMENT:
            {', '.join(topics)}
            
            INSTRUCTIONS:
            1. Create a significantly enhanced version of the original documentation
            2. Integrate information from the referenced sources where relevant
            3. Add new sections that provide deeper technical insights
            4. Include practical examples, use cases, and implementation details
            5. Add troubleshooting sections and best practices
            6. Expand on the key topics with current information
            7. Maintain the original structure but significantly expand each section
            8. Add relevant technical specifications and requirements
            9. Include security considerations and performance optimizations
            10. Create a comprehensive reference that goes far beyond the original
            
            Generate a professional, well-structured enhanced documentation that would be valuable for developers and technical professionals.
            Use markdown formatting for better readability.
            """
            
            messages = [
                {"role": "system", "content": "You are an expert technical documentation specialist who creates comprehensive, professional documentation that significantly enhances original materials with deeper insights, practical examples, and current best practices."},
                {"role": "user", "content": prompt}
            ]
            
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=8000  # Increased for comprehensive response
            )
            
            enhanced_content = response.choices[0].message.content
            
            # Add enhancement metadata
            enhancement_footer = f"""

---

## Enhancement Summary

**Enhanced by:** SuperCrawler AI Document Enhancement  
**Enhancement Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**AI Model:** {model}  
**Sources Processed:** {len(scraped_data)} referenced URLs  
**Topics Researched:** {', '.join(topics)}  

**Enhancement Process:**
1. ✅ Analyzed original documentation ({len(original_content)} characters)
2. ✅ Extracted and processed {len(scraped_data)} referenced sources
3. ✅ Identified key topics for research: {', '.join(topics)}
4. ✅ Generated comprehensive enhanced documentation with AI
5. ✅ Integrated additional context and practical insights

This enhanced documentation provides significantly more depth, practical examples, and current best practices compared to the original version.
"""
            
            return enhanced_content + enhancement_footer
            
        except Exception as e:
            self.logger.error(f"Error generating enhanced documentation: {str(e)}")
            return f"Error generating enhanced documentation: {str(e)}\n\nOriginal content:\n{original_content}"
    
    @exception_handler
    def generate_report(self, data, report_type="detailed", model="deepseek-reasoner"):
        """
        Generate a detailed report from scraped data
        
        Args:
            data (list): List of scraped page data
            report_type (str): Type of report to generate
            model (str): DeepSeek model to use
        
        Returns:
            dict: Generated report
        """
        try:
            self.status_updated.emit("Generating report from scraped data...")
            
            # Prepare data for analysis
            urls = [item.get('url', '') for item in data]
            titles = [item.get('title', '') for item in data]
            
            # Concatenate content from all pages (with reasonable limit)
            all_content = "\n\n".join([
                f"Page: {item.get('title', 'No title')}\nURL: {item.get('url', 'No URL')}\n\n{item.get('content', '')[:3000]}" 
                for item in data
            ])
            
            # Truncate if too large
            if len(all_content) > 30000:  # Keep within token limits
                all_content = all_content[:30000] + "... [content truncated]"
            
            # Create prompt based on report type
            if report_type == "detailed":
                prompt = f"""
                Generate a detailed report from the following webpage data. 
                Include the following sections:
                1. Executive Summary
                2. Key Topics & Entities
                3. Main Findings
                4. Insights & Recommendations
                5. Data Structure Overview
                
                Sources: {urls}
                Titles: {titles}
                
                Content:
                {all_content}
                """
            elif report_type == "seo":
                prompt = f"""
                Analyze the following web content from an SEO perspective.
                Include:
                1. Keyword Analysis
                2. Content Quality Assessment
                3. Meta Information Review
                4. Linking Structure Insights
                5. SEO Recommendations
                
                Sources: {urls}
                Content:
                {all_content}
                """
            else:
                prompt = f"""
                Provide a concise summary of the key information found in these webpages:
                
                Sources: {urls}
                Content:
                {all_content}
                """
            
            # Call DeepSeek API
            if not hasattr(self, 'client'):
                self.logger.warning("API client not properly configured, initializing with current settings")
                from openai import OpenAI
                self.client = OpenAI(
                    api_key=self.api_key,
                    base_url="https://api.deepseek.com"
                )

            try:
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "You are an expert data analyst and report generator. Create comprehensive, well-structured reports from web content."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.3,
                    max_tokens=4000
                )
                
                report_content = response.choices[0].message.content
                
                # Structure the report
                report = {
                    "title": f"SuperCrawler {report_type.title()} Report",
                    "report_type": report_type,
                    "sources": urls,
                    "content": report_content,
                    "generated_at": datetime.now().isoformat(),
                    "page_count": len(data),
                    "model_used": model,
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    }
                }
                
                self.status_updated.emit("Report generated successfully")
                self.analysis_completed.emit({"report": report})
                return report
            
            except Exception as e:
                error_id = log_error(e, "Error generating report")
                self.error_occurred.emit(f"Report generation error [{error_id}]: {str(e)}")
                return None
            
        except Exception as e:
            error_id = log_error(e, "Error generating report")
            self.error_occurred.emit(f"Report generation error [{error_id}]: {str(e)}")
            return None
    
    def start_ai_agent(self, start_url, max_pages=10, search_topic=None, depth=2):
        """
        Start the AI agent to crawl and analyze content
        
        Args:
            start_url (str): Starting URL
            max_pages (int): Maximum number of pages to scrape
            search_topic (str): Optional search topic to focus on
            depth (int): Maximum depth of crawling
        """
        try:
            if self.agent_active:
                self.logger.warning("AI agent already running, stopping current agent")
                self.stop_ai_agent()
                
            # Check for API key
            if not self.api_key:
                self.error_occurred.emit("API key is not set. Please configure your DeepSeek API key in settings.")
                return False
                
            # Ensure client is initialized before starting agent
            if not self.client:
                success = self._setup_client()
                if not success:
                    self.error_occurred.emit("Failed to initialize AI client. Please check your API key and connection.")
                    return False
            
            # Create and start agent thread
            self.logger.info(f"Starting AI agent for URL: {start_url}")
            self.agent_thread = AIAgentThread(self, start_url, max_pages, search_topic, depth)
            
            # Connect signals
            self.agent_thread.progress_update.connect(self._on_agent_progress)
            self.agent_thread.crawl_completed.connect(self._on_agent_completed)
            self.agent_thread.error_occurred.connect(self._on_agent_error)
            
            # Start thread
            self.agent_thread.start()
            self.agent_active = True
            
            self.status_updated.emit(f"AI agent started for {start_url}")
            return True
            
        except Exception as e:
            error_id = log_error(e, "Failed to start AI agent")
            self.error_occurred.emit(f"Failed to start AI agent [{error_id}]: {str(e)}")
            return False
    
    def stop_ai_agent(self):
        """Stop the running AI agent"""
        if not self.agent_active or not self.agent_thread:
            self.status_updated.emit("No AI agent is currently running")
            return
        
        self.agent_thread.stop()
        self.status_updated.emit("AI agent stopping...")
    
    def _on_agent_progress(self, message, progress):
        """Handle agent progress updates"""
        self.agent_progress.emit(message, progress)
        self.status_updated.emit(message)
    
    def _on_agent_completed(self, data):
        """Handle agent completion"""
        self.agent_active = False
        self.agent_completed.emit(data)
        self.status_updated.emit(f"AI agent completed, collected {len(data)} pages")
    
    def _on_agent_error(self, error_message):
        """Handle agent errors"""
        self.agent_active = False
        self.error_occurred.emit(error_message)
    
    def get_available_models(self):
        """Get available DeepSeek models with their descriptions"""
        return self.AVAILABLE_MODELS
    
    def is_api_key_set(self):
        """Check if an API key is set - with config reload"""
        # Always check config first to catch updates
        try:
            self.config = load_config()
            fresh_api_key = self.config.get("deepseek_api_key", "")
            
            # Update instance if needed
            if fresh_api_key and fresh_api_key != self.api_key:
                self.logger.info("Updating API key from fresh config")
                self.api_key = fresh_api_key
                self._setup_client()
                
            return bool(self.api_key)
        except Exception as e:
            self.logger.error(f"Error in is_api_key_set: {str(e)}")
            return bool(self.api_key)  # Fallback to current state
    
    def get_api_key(self):
        """Get the current API key"""
        return self.api_key
    
    @exception_handler
    def analyze_website(self, url, max_pages=10, depth=2, topic=None, model="deepseek-chat"):
        """
        Analyze a website using DeepSeek AI
        
        Args:
            url (str): URL to analyze
            max_pages (int): Maximum number of pages to crawl
            depth (int): Crawl depth
            topic (str): Topic to focus analysis on
            model (str): DeepSeek model to use
        
        Returns:
            list: Analyzed data
        """
        if not self.api_key:
            self.error_occurred.emit("DeepSeek API key not set. Please configure in settings.")
            return None
        
        try:
            # First, start a standard crawl to get the content
            from intellicrawler.crawler import Crawler
            crawler = Crawler()
            
            # Connect signals
            crawler.progress_updated.connect(self._on_crawl_progress)
            crawler.status_updated.connect(self.status_updated.emit)
            crawler.error_occurred.connect(self.error_occurred.emit)
            
            # Start the crawl
            self.status_updated.emit(f"Crawling {url} for AI analysis...")
            scraped_data = []
            
            # Create a simple thread to run the crawl
            import threading
            
            # Event to signal when crawl is done
            crawl_done = threading.Event()
            
            def collect_page(page_data):
                scraped_data.append(page_data)
            
            def on_crawl_complete(data):
                nonlocal scraped_data
                scraped_data = data
                crawl_done.set()
            
            # Connect the signals
            crawler.page_scraped.connect(collect_page)
            crawler.crawl_completed.connect(on_crawl_complete)
            
            # Try to auto-detect Chrome on common paths
            chrome_path = None
            import os
            common_chrome_paths = [
                # Windows paths
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                # macOS paths
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                # Linux paths
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium"
            ]
            
            for path in common_chrome_paths:
                if os.path.exists(path):
                    chrome_path = path
                    self.status_updated.emit(f"Found Chrome at: {chrome_path}")
                    break
            
            # First try with dynamic mode (JavaScript rendering)
            dynamic_mode = True
            try_without_dynamic = False
            
            # Start crawling in a separate thread
            crawl_thread = threading.Thread(
                target=crawler.crawl,
                args=(url, max_pages, depth, dynamic_mode, 1.0),
                kwargs={"chrome_path": chrome_path}
            )
            crawl_thread.daemon = True
            crawl_thread.start()
            
            # Wait for crawl to complete with a timeout
            if not crawl_done.wait(timeout=30):  # 30 second timeout
                # If it times out, it might be a Selenium issue, try without dynamic mode
                self.status_updated.emit("Dynamic crawling timed out. Trying without JavaScript rendering...")
                try_without_dynamic = True
            
            # If dynamic crawling failed or timed out, try without it
            if try_without_dynamic or (not scraped_data and dynamic_mode):
                # Clear any previous data
                scraped_data = []
                crawl_done.clear()

                # Reuse the existing crawler instance instead of creating a new one
                # Disconnect previous signals to avoid duplicates
                try:
                    crawler.page_scraped.disconnect()
                    crawler.crawl_completed.disconnect()
                except:
                    pass  # Ignore if already disconnected

                # Reconnect signals
                crawler.page_scraped.connect(collect_page)
                crawler.crawl_completed.connect(on_crawl_complete)

                # Start without dynamic mode
                crawl_thread = threading.Thread(
                    target=crawler.crawl,
                    args=(url, max_pages, depth, False, 1.0),  # dynamic=False
                    kwargs={}
                )
                crawl_thread.daemon = True
                crawl_thread.start()

                # Wait again with a timeout
                if not crawl_done.wait(timeout=180):  # 3 minute timeout
                    self.error_occurred.emit("Crawl timed out. Try with fewer pages or less depth.")
                    return None
            
            # Check if we have data
            if not scraped_data:
                self.error_occurred.emit("No data was scraped. Please check the URL and try again.")
                return None
            
            # Process each page with AI
            self.status_updated.emit(f"Analyzing {len(scraped_data)} pages with DeepSeek AI...")
            
            # Prepare the content for analysis
            all_content = ""
            for i, page in enumerate(scraped_data):
                all_content += f"\n\n--- PAGE {i+1}: {page['url']} ---\n"
                all_content += page.get('content', page.get('text', ''))  # Try both content and text keys
                
                # Update progress
                progress = int((i+1) / len(scraped_data) * 50)  # First 50% is crawling
                self.agent_progress.emit(f"Preparing content from page {i+1}/{len(scraped_data)}", 50 + progress)
            
            # Perform analysis
            if not topic:
                topic = "general website content"
                
            analysis_prompt = f"""
            Analyze the following website content from {url}.
            
            Focus on: {topic}
            
            Content:
            {all_content[:50000]}  # Limit content to avoid token limits
            
            Please provide:
            1. A summary of the website
            2. Key topics covered
            3. Important insights related to "{topic}"
            4. Organization of the website
            5. Recommendations for further exploration
            """
            
            # Use analyze_content method
            analysis_result = self.analyze_content(
                content=analysis_prompt,
                model=model,
                analysis_type="detailed",
                temperature=0.3
            )
            
            if not analysis_result:
                self.error_occurred.emit("Failed to analyze content with AI.")
                return None
                
            # Add the analysis to each page
            for page in scraped_data:
                page['ai_analysis'] = analysis_result['result']
            
            # Signal completion
            self.agent_progress.emit("AI analysis completed", 100)
            self.agent_completed.emit(scraped_data)
            
            return scraped_data
            
        except Exception as e:
            error_id = log_error(e, "Error during website analysis")
            self.error_occurred.emit(f"Analysis error [{error_id}]: {str(e)}")
            return None
    
    def _on_crawl_progress(self, current, total):
        """Handle crawl progress updates"""
        if total > 0:
            progress = int((current / total) * 50)  # First 50% of the process
            self.agent_progress.emit(f"Crawling page {current}/{total}", progress)
    
    def analyze_url_context(self, url):
        """
        Analyze URL to detect context and suggest optimal crawling strategy
        
        Args:
            url (str): URL to analyze
            
        Returns:
            dict: Context analysis with suggestions
        """
        try:
            context = {
                'url': url,
                'detected_type': 'unknown',
                'confidence': 0.0,
                'suggested_mode': 'general',
                'focus_suggestions': [],
                'constraints_suggestions': {},
                'reasoning': ''
            }
            
            url_lower = url.lower()
            
            # Documentation site patterns
            doc_patterns = [
                'docs', 'documentation', 'learn', 'guide', 'api', 'reference',
                'manual', 'help', 'tutorial', 'readme', 'wiki'
            ]
            
            # DeepSeek API documentation specific
            if 'api-docs.deepseek.com' in url_lower:
                context['detected_type'] = 'deepseek_api_docs'
                context['confidence'] = 0.98
                context['suggested_mode'] = 'documentation'
                context['focus_suggestions'] = ['API reference', 'DeepSeek AI', 'technical documentation', 'endpoints', 'examples']
                context['constraints_suggestions'] = {
                    'stay_domain': True,
                    'max_pages': 100,  # Higher for comprehensive API docs
                    'depth': 5,        # Deeper for nested documentation
                    'follow_nav_patterns': True
                }
                context['reasoning'] = 'DeepSeek API documentation detected - optimized for comprehensive API reference crawling'

            # Microsoft Learn specific
            elif 'learn.microsoft.com' in url_lower:
                context['detected_type'] = 'microsoft_learn'
                context['confidence'] = 0.95
                context['suggested_mode'] = 'documentation'
                context['focus_suggestions'] = ['technical documentation', 'API reference', 'developer guides']
                context['constraints_suggestions'] = {
                    'stay_domain': True,
                    'max_pages': 50,
                    'depth': 4,
                    'follow_nav_patterns': True
                }
                context['reasoning'] = 'Microsoft Learn detected - optimized for structured technical documentation'
                
            # GitHub documentation
            elif 'github.com' in url_lower and any(p in url_lower for p in ['wiki', 'docs']):
                context['detected_type'] = 'github_docs'
                context['confidence'] = 0.9
                context['suggested_mode'] = 'documentation'
                context['focus_suggestions'] = ['project documentation', 'API reference', 'usage examples']
                
            # General documentation sites
            elif any(pattern in url_lower for pattern in doc_patterns):
                context['detected_type'] = 'documentation'
                context['confidence'] = 0.8
                context['suggested_mode'] = 'documentation'
                context['focus_suggestions'] = ['technical content', 'guides', 'reference material']
                
            # Developer resources
            elif any(pattern in url_lower for pattern in ['developer', 'dev', 'sdk']):
                context['detected_type'] = 'developer_resource'
                context['confidence'] = 0.75
                context['suggested_mode'] = 'research'
                context['focus_suggestions'] = ['development guides', 'SDK documentation', 'code examples']
                
            # Tutorial/learning sites
            elif any(pattern in url_lower for pattern in ['tutorial', 'course', 'lesson', 'example']):
                context['detected_type'] = 'educational'
                context['confidence'] = 0.7
                context['suggested_mode'] = 'targeted_content'
                context['focus_suggestions'] = ['tutorials', 'examples', 'step-by-step guides']
                
            # Emit signal with detected context
            self.context_detected.emit(context)
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error analyzing URL context: {str(e)}")
            return None
    
    def create_intelligent_crawl_strategy(self, url, mode, intelligence_level, focus_topic=None):
        """Create an intelligent crawling strategy with enhanced focus topic extraction"""
        try:
            # Analyze URL context first
            context = self.analyze_url_context(url)
            
            # Enhanced focus topic extraction
            if not focus_topic:
                # Try to extract from URL context
                if context and 'suggested_focus' in context:
                    focus_topic = context['suggested_focus']
                else:
                    # Extract from URL path
                    from urllib.parse import urlparse
                    parsed_url = urlparse(url)
                    path_parts = [part for part in parsed_url.path.split('/') if part and len(part) > 2]
                    
                    # Look for meaningful path components
                    meaningful_parts = []
                    skip_words = {'docs', 'documentation', 'en-us', 'latest', 'stable', 'v1', 'api', 'www', 'learn'}
                    
                    for part in path_parts:
                        clean_part = part.replace('-', ' ').replace('_', ' ').lower()
                        if clean_part not in skip_words:
                            meaningful_parts.append(clean_part)
                    
                    if meaningful_parts:
                        focus_topic = ' '.join(meaningful_parts[-2:])  # Last 2 meaningful parts
                    else:
                        focus_topic = 'general information'
            
            # Extract and enhance keywords from focus topic
            focus_keywords = []
            if focus_topic:
                import re
                words = re.findall(r'\b\w+\b', focus_topic.lower())
                
                # Filter meaningful words
                meaningful_words = [word for word in words if len(word) > 2 and 
                                  word not in {'the', 'and', 'for', 'with', 'how', 'what', 'when', 'where', 'why'}]
                
                focus_keywords = meaningful_words[:10]
                
                # Add domain-specific keywords
                if any(word in focus_topic.lower() for word in ['remote', 'desktop', 'control', 'rdp']):
                    focus_keywords.extend(['rdp', 'terminal', 'connection', 'session', 'windows', 'activex'])
                if any(word in focus_topic.lower() for word in ['api', 'programming', 'development']):
                    focus_keywords.extend(['method', 'function', 'endpoint', 'parameter', 'code'])
            
            # Create enhanced strategy
            strategy = {
                'url': url,
                'mode': mode,
                'intelligence_level': intelligence_level,
                'focus_topic': focus_topic,
                'focus_keywords': focus_keywords,
                'context': context,
                'created_at': datetime.now().isoformat()
            }
            
            # Mode-specific configurations
            if mode == 'documentation':
                strategy.update({
                    'crawl_instructions': [
                        f"Focus on technical documentation about {focus_topic}",
                        "Follow structured navigation patterns",
                        "Prioritize content with code examples and API references",
                        "Crawl all documentation sections comprehensively",
                        "Include quick start guides, API endpoints, and examples",
                        "Avoid marketing and general support pages"
                    ],
                    'quality_filters': [
                        "Contains technical content",
                        "Has structured headings",
                        "Includes code examples or API references",
                        f"Content relates to {focus_topic}"
                    ],
                    'preferred_paths': ['/docs/', '/documentation/', '/guide/', '/tutorial/', '/reference/', '/api/', '/quick_start/', '/examples/', '/endpoints/'],
                    'avoid_patterns': ['blog', 'news', 'marketing', 'about', 'contact', 'privacy', 'terms'],
                    'max_links_per_page': 15  # Higher for comprehensive documentation crawling
                })
                
            elif mode == 'research':
                strategy.update({
                    'crawl_instructions': [
                        f"Comprehensive research on {focus_topic}",
                        "Follow related content links",
                        "Collect diverse perspectives and examples",
                        "Include case studies and implementations"
                    ],
                    'quality_filters': [
                        "Contains analytical content",
                        "Provides detailed explanations",
                        f"Relevant to {focus_topic} research"
                    ],
                    'preferred_paths': ['/research/', '/article/', '/blog/', '/study/', '/whitepaper/'],
                    'avoid_patterns': ['tutorial', 'quickstart', 'getting-started']
                })
                
            else:  # targeted_content
                strategy.update({
                    'crawl_instructions': [
                        f"Target specific content about {focus_topic}",
                        "Extract tutorials, guides, and examples",
                        "Prioritize actionable information",
                        "Filter out generic content"
                    ],
                    'quality_filters': [
                        "Contains specific information",
                        "Provides actionable content",
                        f"Directly addresses {focus_topic}"
                    ],
                    'preferred_paths': ['/tutorial/', '/guide/', '/howto/', '/example/', '/demo/'],
                    'avoid_patterns': ['overview', 'introduction', 'general']
                })
            
            # Intelligence level adjustments
            strategy['quality_threshold'] = 4 + intelligence_level  # 5-9 range
            strategy['max_links_per_page'] = min(15, 2 + intelligence_level * 2)
            strategy['content_analysis_depth'] = 'deep' if intelligence_level >= 4 else 'standard'
            
            # Emit strategy decision
            strategy_summary = f"🎯 Created {mode} strategy targeting '{focus_topic}' with {len(focus_keywords)} keywords"
            self.crawl_strategy_decided.emit(strategy_summary)
            
            return strategy
            
        except Exception as e:
            self.logger.error(f"Error creating crawl strategy: {str(e)}")
            # Return basic fallback strategy
            return {
                'url': url,
                'mode': mode,
                'intelligence_level': intelligence_level,
                'focus_topic': focus_topic or 'general information',
                'focus_keywords': [focus_topic] if focus_topic else ['information'],
                'quality_threshold': 5,
                'max_links_per_page': 10  # Increased from 5 to 10 for better documentation coverage
            }
    
    def assess_page_quality(self, page_data, strategy):
        """
        Use AI to assess the quality and relevance of a scraped page
        
        Args:
            page_data (dict): Page data with content, title, url
            strategy (dict): Crawling strategy with quality criteria
            
        Returns:
            dict: Quality assessment with score and reasoning
        """
        try:
            if not self.client:
                return {'score': 5, 'reasoning': 'AI assessment unavailable', 'relevant': True}
            
            # Extract key information
            title = page_data.get('title', 'Unknown')
            url = page_data.get('url', 'Unknown')
            content = page_data.get('content', '')[:2000]  # Limit content for analysis
            
            # Create assessment prompt
            quality_criteria = strategy.get('quality_filters', [])
            focus_topic = strategy.get('focus_topic', 'general content')
            
            prompt = f"""
            Analyze this web page for quality and relevance:
            
            Title: {title}
            URL: {url}
            Content Preview: {content}
            
            Quality Criteria:
            {chr(10).join(f"- {criterion}" for criterion in quality_criteria)}
            
            Focus Topic: {focus_topic}
            
            Rate this page from 1-10 for relevance and quality, and explain why.
            Consider:
            1. Content depth and technical accuracy
            2. Relevance to the focus topic
            3. Presence of actionable information
            4. Overall usefulness for developers
            
            Respond in this format:
            SCORE: [1-10]
            REASONING: [brief explanation]
            RELEVANT: [yes/no]
            """
            
            try:
                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=300,
                    temperature=0.1
                )
                
                ai_response = response.choices[0].message.content
                
                # Parse AI response
                score = 5  # default
                reasoning = "AI analysis completed"
                relevant = True
                
                for line in ai_response.split('\n'):
                    if line.startswith('SCORE:'):
                        try:
                            score = int(line.split(':')[1].strip())
                        except:
                            pass
                    elif line.startswith('REASONING:'):
                        reasoning = line.split(':', 1)[1].strip()
                    elif line.startswith('RELEVANT:'):
                        relevant = 'yes' in line.lower()
                
                assessment = {
                    'score': max(1, min(10, score)),
                    'reasoning': reasoning,
                    'relevant': relevant,
                    'ai_response': ai_response
                }
                
                # Emit quality assessment
                self.page_quality_assessed.emit(url, score)
                
                return assessment
                
            except Exception as e:
                self.logger.error(f"AI quality assessment failed: {str(e)}")
                return {'score': 5, 'reasoning': f'Assessment error: {str(e)}', 'relevant': True}
                
        except Exception as e:
            self.logger.error(f"Error in page quality assessment: {str(e)}")
            return {'score': 5, 'reasoning': 'Assessment failed', 'relevant': True}
    
    def start_enhanced_ai_agent(self, url, crawl_mode='documentation', intelligence_level=3,
                               focus_topic=None, max_pages=50, depth=4, stay_domain=True,
                               smart_nav=True, model="deepseek-reasoner"):
        """Start enhanced AI agent with intelligent crawling capabilities"""
        try:
            self.logger.info(f"Starting enhanced AI agent for {url}")
            
            # Create intelligent crawl strategy
            strategy = self.create_intelligent_crawl_strategy(
                url, crawl_mode, intelligence_level, focus_topic
            )
            
            # Create and start enhanced agent thread
            self.agent_thread = EnhancedAIAgentThread(
                ai_instance=self,
                start_url=url,
                strategy=strategy,
                max_pages=max_pages,
                depth=depth,
                stay_domain=stay_domain,
                smart_nav=smart_nav,
                model=model
            )
            
            # Connect thread signals
            self.agent_thread.progress_update.connect(self._on_agent_progress)
            self.agent_thread.crawl_completed.connect(self._on_agent_completed)
            self.agent_thread.error_occurred.connect(self._on_agent_error)
            self.agent_thread.intelligent_decision.connect(self.intelligent_decision.emit)
            
            # Start thread
            self.agent_thread.start()
            self.agent_active = True
            
            self.logger.info("Enhanced AI agent thread started successfully")
            return True

        except Exception as e:
            error_msg = f"Failed to start enhanced AI agent: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    def assess_page_quality_async(self, page_data, strategy, callback):
        """Assess page quality asynchronously to avoid blocking UI"""
        def run_assessment():
            try:
                result = self.assess_page_quality(page_data, strategy)
                callback(result)
            except Exception as e:
                self.logger.error(f"Error in async page quality assessment: {str(e)}")
                # Return default assessment on error
                callback({
                    'score': 5,
                    'reasoning': 'Assessment failed, using default score',
                    'relevant': True
                })
        
        # Run in separate thread to avoid blocking
        import threading
        thread = threading.Thread(target=run_assessment)
        thread.daemon = True
        thread.start()

    def make_intelligent_crawl_decisions(self, current_url, found_links, strategy):
        """Make intelligent decisions about which links to crawl next"""
        try:
            if not self.client or not self.api_key:
                # Fallback to simple heuristic
                return self._fallback_link_selection(found_links, strategy)
            
            # Use AI to intelligently select next links
            context = f"""
You are an intelligent web crawler making decisions about which links to crawl.

Current URL: {current_url}
Crawl Strategy: {strategy['mode']} mode
Focus Topic: {strategy.get('focus_topic', 'General information')}
Intelligence Level: {strategy.get('intelligence_level', 3)}/5

Available Links ({len(found_links)} total):
{chr(10).join([f"- {link}" for link in found_links[:20]])}  # Limit to first 20 for context

Your task: Select the 3-5 most relevant links that will lead to valuable content related to: "{strategy.get('focus_topic', 'the topic of interest')}"

Consider:
1. Relevance to the focus topic
2. Likelihood of containing detailed information
3. Navigation patterns (docs, guides, tutorials)
4. Avoiding duplicate or low-value pages

Respond with a JSON array of the selected URLs, like: ["url1", "url2", "url3"]
Only include URLs that are most likely to contain valuable, relevant content.
"""

            try:
                response = self.client.chat.completions.create(
                    model=strategy.get('model', 'deepseek-chat'),
                    messages=[
                        {"role": "system", "content": "You are an intelligent web crawling assistant. Always respond with valid JSON."},
                        {"role": "user", "content": context}
                    ],
                    temperature=0.1,
                    max_tokens=500
                )
                
                selected_links = json.loads(response.choices[0].message.content.strip())
                
                # Validate that selected links are in the original list
                valid_links = [link for link in selected_links if link in found_links]

                if valid_links:
                    # For documentation sites, allow more links per page
                    max_links = strategy.get('max_links_per_page', 10)  # Increased from 5 to 10
                    return valid_links[:max_links]
                else:
                    return self._fallback_link_selection(found_links, strategy)
                    
            except Exception as e:
                self.logger.warning(f"AI link selection failed: {str(e)}, using fallback")
                return self._fallback_link_selection(found_links, strategy)
                
        except Exception as e:
            self.logger.error(f"Error in intelligent crawl decisions: {str(e)}")
            return self._fallback_link_selection(found_links, strategy)
    
    def _fallback_link_selection(self, found_links, strategy):
        """Enhanced fallback link selection using strategy-based heuristics"""
        focus_keywords = strategy.get('focus_keywords', [])
        preferred_paths = strategy.get('preferred_paths', [])
        avoid_patterns = strategy.get('avoid_patterns', [])
        
        scored_links = []
        for link in found_links:
            score = 0
            link_lower = link.lower()
            
            # Score based on focus keywords (higher weight)
            for keyword in focus_keywords:
                if keyword.lower() in link_lower:
                    score += 5
            
            # Prefer strategy-specific paths
            for path in preferred_paths:
                if path.lower() in link_lower:
                    score += 4
            
            # Avoid unwanted patterns
            for pattern in avoid_patterns:
                if pattern.lower() in link_lower:
                    score -= 5
            
            # General quality indicators
            quality_indicators = ['example', 'sample', 'demo', 'implementation', 'code', 'technical', 'endpoint', 'method', 'parameter']
            for indicator in quality_indicators:
                if indicator in link_lower:
                    score += 2

            # API documentation specific indicators
            api_indicators = ['api', 'reference', 'endpoint', 'method', 'request', 'response', 'parameter', 'authentication']
            for indicator in api_indicators:
                if indicator in link_lower:
                    score += 3  # Higher score for API content

            # Penalize common low-value pages
            low_value = ['login', 'register', 'contact', 'about', 'privacy', 'terms', 'cookie', 'legal']
            for pattern in low_value:
                if pattern in link_lower:
                    score -= 3

            # Bonus for structured content indicators
            if any(word in link_lower for word in ['overview', 'getting-started', 'quickstart', 'quick_start']):
                score += 2  # Increased bonus for important starting points
            
            scored_links.append((link, score))
        
        # Sort by score and return top links
        scored_links.sort(key=lambda x: x[1], reverse=True)
        max_links = strategy.get('max_links_per_page', 10)  # Increased from 5 to 10 for better coverage

        # Only return links with positive scores
        good_links = [link for link, score in scored_links if score > 0]
        return good_links[:max_links]


class AIAgentThread(QThread):
    """Thread for running the AI agent in the background"""
    
    # Signals
    progress_update = pyqtSignal(str, int)  # message, progress percentage
    crawl_completed = pyqtSignal(list)  # list of scraped data
    error_occurred = pyqtSignal(str)
    
    def __init__(self, ai_instance, start_url, max_pages=10, search_topic=None, depth=2):
        """
        Initialize the AI agent thread
        
        Args:
            ai_instance (DeepSeekAI): The AI instance to use for API calls
            start_url (str): Starting URL for the crawler
            max_pages (int): Maximum number of pages to scrape
            search_topic (str): Topic to focus the search on
            depth (int): Maximum crawl depth
        """
        super().__init__()
        self.ai = ai_instance
        self.start_url = start_url
        self.max_pages = max_pages
        self.search_topic = search_topic
        self.depth = depth
        self.should_stop = False
        self.logger = get_logger()
        
        # Import here to avoid circular imports
        from intellicrawler.crawler import Crawler
        self.crawler = Crawler()
        
        # Connect crawler signals
        self.crawler.progress_updated.connect(self._on_crawler_progress)
        self.crawler.status_updated.connect(self._on_crawler_status)
        self.crawler.error_occurred.connect(self._on_crawler_error)
    
    def run(self):
        """Run the AI agent"""
        try:
            self.logger.info(f"Starting AI agent with URL: {self.start_url}")
            self.progress_update.emit("Initializing AI agent...", 0)
            
            scraped_data = []
            
            # Step 1: Plan the crawl strategy with AI
            if self.search_topic:
                self.progress_update.emit(f"Planning crawl strategy for topic: {self.search_topic}", 5)
                crawl_strategy = self._plan_crawl_strategy()
            else:
                crawl_strategy = {"max_pages": self.max_pages, "depth": self.depth}
            
            # Step 2: Execute the crawl
            self.progress_update.emit("Starting web crawling...", 10)
            
            max_pages = crawl_strategy.get("max_pages", self.max_pages)
            depth = crawl_strategy.get("depth", self.depth)
            
            self.logger.info(f"Crawling with parameters: max_pages={max_pages}, depth={depth}, url={self.start_url}")
            
            try:
                # Reset crawler state
                self.crawler.visited_urls = set()
                self.crawler.scraped_data = []
                self.crawler.should_stop = False
                
                # Initialize selenium for dynamic rendering
                try:
                    self.crawler.initialize_selenium()
                except Exception as e:
                    self.logger.warning(f"Failed to initialize Selenium: {str(e)}. Falling back to static crawling.")
                    self.progress_update.emit("Warning: Dynamic rendering not available, falling back to static crawling", 15)
                
                # Execute crawl (this will run in this thread but handle its own async operations)
                self.crawler.crawl(
                    self.start_url, 
                    max_pages=max_pages,
                    depth=depth,
                    dynamic=True, 
                    delay=1.0
                )
                
                # Wait for crawling to complete or stop signal
                while not hasattr(self.crawler, 'crawl_completed') or not self.crawler.crawl_completed:
                    if self.should_stop:
                        self.crawler.stop()
                        self.progress_update.emit("AI agent stopped by user", 100)
                        return
                    self.msleep(100)  # Small sleep to prevent CPU hogging
                
                # Get the scraped data
                scraped_data = self.crawler.scraped_data
                
                if not scraped_data:
                    self.error_occurred.emit("No data was scraped. The URL may be invalid or blocked crawling.")
                    return
            except Exception as e:
                error_msg = f"Crawling error: {str(e)}"
                self.logger.error(error_msg, exc_info=True)
                self.error_occurred.emit(error_msg)
                return
                
            # Step 3: Analyze the content with AI
            self.progress_update.emit("Processing scraped content...", 70)
            
            # Try to generate summaries but don't fail if API access fails
            api_available = False
            
            # Check if we can use the AI
            if self.ai and self.ai.api_key and self.ai.client:
                # Check API connectivity with a small test
                try:
                    self.progress_update.emit("Testing API connection...", 72)
                    test_result = self.ai.check_api_key()
                    if test_result:
                        api_available = True
                        self.progress_update.emit("API connection successful, generating summaries...", 75)
                    else:
                        self.progress_update.emit("API test failed, skipping AI analysis", 75)
                except Exception as e:
                    self.logger.warning(f"API test failed: {str(e)}")
                    self.progress_update.emit("API test failed, continuing without AI summaries", 75)
            
            # Generate summaries for each page
            for i, page in enumerate(scraped_data):
                if self.should_stop:
                    break
                    
                # Skip pages with minimal content
                if len(page.get("content", "")) < 100:
                    continue
                
                # Calculate progress percentage
                progress = 75 + int((i / len(scraped_data)) * 20)
                
                # Try AI summary if available, otherwise create a basic summary
                if api_available:
                    try:
                        self.progress_update.emit(f"Analyzing page {i+1} of {len(scraped_data)}", progress)
                        
                        analysis = self.ai.analyze_content(
                            page["content"], 
                            model="deepseek-chat",
                            analysis_type="summary"
                        )
                        
                        if analysis and "content" in analysis:
                            page["ai_summary"] = analysis["content"]
                        else:
                            # Create a basic summary by extracting the first 200 characters
                            content = page.get("content", "").strip()
                            page["ai_summary"] = content[:200] + "..." if len(content) > 200 else content
                            
                    except Exception as e:
                        self.logger.warning(f"Error analyzing page {i+1}: {str(e)}")
                        # Create a basic summary
                        content = page.get("content", "").strip()
                        page["ai_summary"] = content[:200] + "..." if len(content) > 200 else content
                else:
                    # Create a basic summary without AI
                    self.progress_update.emit(f"Processing page {i+1} of {len(scraped_data)}", progress)
                    content = page.get("content", "").strip()
                    page["ai_summary"] = content[:200] + "..." if len(content) > 200 else content
            
            # Step 4: Finalize and return results
            self.progress_update.emit("AI agent completed successfully", 100)
            self.crawl_completed.emit(scraped_data)
            
        except Exception as e:
            error_id = log_error(e, "Error in AI agent")
            self.error_occurred.emit(f"AI agent error [{error_id}]: {str(e)}")
    
    def stop(self):
        """Stop the AI agent"""
        self.should_stop = True
        if self.crawler:
            self.crawler.stop()
    
    def _plan_crawl_strategy(self):
        """Use AI to plan the crawl strategy based on the search topic"""
        try:
            if not self.search_topic:
                return {"max_pages": self.max_pages, "depth": self.depth}

            # Simple validation to avoid unnecessary API calls
            if not self.ai or not self.ai.api_key:
                self.logger.warning("API client not properly configured, using default crawl strategy")
                self.progress_update.emit("Using default crawl strategy (API key not set)", 5)
                return {"max_pages": self.max_pages, "depth": self.depth, "priority_keywords": [self.search_topic.split()]}

            # Ensure AI client is set up
            if not self.ai.client:
                success = self.ai._setup_client()
                if not success:
                    self.logger.warning("Failed to set up AI client, using default strategy")
                    self.progress_update.emit("Using default crawl strategy (API client setup failed)", 5)
                    return {"max_pages": self.max_pages, "depth": self.depth, "priority_keywords": [self.search_topic.split()]}

            # Build a simple crawl strategy without using AI if there are authentication issues
            try:
                # First try a simplified strategy
                self.progress_update.emit("Planning crawl strategy...", 5)
                
                # Create a simple strategy based on the search topic
                keywords = [word for word in self.search_topic.split() if len(word) > 3]
                
                # Use a reasonable default strategy
                strategy = {
                    "max_pages": min(self.max_pages, 20),  # Default to 20 pages or user-specified max
                    "depth": min(self.depth, 2),           # Default to depth 2 or user-specified max
                    "priority_keywords": keywords
                }
                
                self.logger.info(f"Using default strategy without API: {strategy}")
                return strategy
                
            except Exception as e:
                self.logger.error(f"Error creating default strategy: {str(e)}", exc_info=True)
                return {"max_pages": self.max_pages, "depth": self.depth, "priority_keywords": []}
                
        except Exception as e:
            self.logger.error(f"General error in strategy planning: {str(e)}", exc_info=True)
            return {"max_pages": self.max_pages, "depth": self.depth}
    
    def _on_crawler_progress(self, current, total):
        """Handle crawler progress updates"""
        if total > 0:
            progress = min(10 + int((current / total) * 60), 70)  # Map to 10-70% of total progress
            self.progress_update.emit(f"Crawling page {current} of {total}", progress)
    
    def _on_crawler_status(self, status):
        """Handle crawler status updates"""
        self.progress_update.emit(status, -1)  # -1 means don't update progress bar
    
    def _on_crawler_error(self, error):
        """Handle crawler errors"""
        self.error_occurred.emit(error) 


class EnhancedAIAgentThread(QThread):
    """Enhanced AI agent thread with intelligent crawling capabilities"""
    
    # Signals
    progress_update = pyqtSignal(str, int)
    crawl_completed = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    intelligent_decision = pyqtSignal(str)
    
    def __init__(self, ai_instance, start_url, strategy, max_pages=50, depth=4,
                 stay_domain=True, smart_nav=True, model="deepseek-reasoner"):
        super().__init__()
        self.ai_instance = ai_instance
        self.start_url = start_url
        self.strategy = strategy
        self.max_pages = max_pages
        self.depth = depth
        self.stay_domain = stay_domain
        self.smart_nav = smart_nav
        self.model = model
        self.should_stop = False
        self.logger = get_logger(__name__)
        self.scraped_data = []
        self.pending_assessments = []
        self.batch_size = 5  # Batch size for AI analysis
        self.page_batch = []  # Temp list for batching
        
    def run(self):
        """Run the enhanced AI agent with intelligent crawling"""
        try:
            self.progress_update.emit("Initializing intelligent AI crawler...", 5)
            
            # Start intelligent crawling process
            self.intelligent_decision.emit(f"🧠 Starting {self.strategy['mode']} crawl for: {self.strategy.get('focus_topic', 'general information')}")
            
            # Use custom intelligent crawling instead of standard crawler
            self._intelligent_crawl()
            
        except Exception as e:
            error_msg = f"Enhanced AI agent error: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
    
    def _intelligent_crawl(self):
        """Perform intelligent crawling using AI decision making"""
        from collections import deque
        
        # Initialize crawling state
        urls_to_visit = deque([(self.start_url, 0)])  # (url, depth)
        visited_urls = set()
        pages_processed = 0
        
        self.progress_update.emit("🔍 Starting intelligent page analysis...", 10)
        
        while urls_to_visit and pages_processed < self.max_pages and not self.should_stop:
            current_url, current_depth = urls_to_visit.popleft()
            
            # Skip if already visited or too deep
            if current_url in visited_urls or current_depth > self.depth:
                continue
                
            visited_urls.add(current_url)
            pages_processed += 1
            
            try:
                # Update progress
                progress = min(20 + int((pages_processed / self.max_pages) * 70), 90)
                self.progress_update.emit(f"📄 Analyzing page {pages_processed}/{self.max_pages}: {current_url[:60]}...", progress)
                
                # Fetch and parse page
                page_data = self._fetch_page(current_url)
                if not page_data:
                    continue
                
                # Add to batch
                self.page_batch.append(page_data)
                
                # If batch is full, analyze batch
                if len(self.page_batch) >= self.batch_size:
                    self._batch_analyze()
                    self.page_batch = []  # Clear batch
                
                # Extract links for next crawl decisions
                if current_depth < self.depth:
                    found_links = self._extract_links(page_data['soup'], current_url)
                    
                    if found_links:
                        # Use AI to intelligently select next links
                        selected_links = self.ai_instance.make_intelligent_crawl_decisions(
                            current_url, found_links, self.strategy
                        )
                        
                        if selected_links:
                            decision_msg = f"🎯 Selected {len(selected_links)} intelligent next steps from {len(found_links)} options"
                            self.intelligent_decision.emit(decision_msg)
                            
                            # Add selected links to crawl queue
                            for link in selected_links:
                                if link not in visited_urls:
                                    urls_to_visit.append((link, current_depth + 1))
                        else:
                            self.intelligent_decision.emit("⚠️ No relevant links found for next crawl step")
                
            except Exception as e:
                self.logger.error(f"Error processing {current_url}: {str(e)}")
            
        # Analyze any remaining pages in batch
        if self.page_batch:
            self._batch_analyze()
        
        self.progress_update.emit("Intelligent crawl completed", 100)
        self.crawl_completed.emit(self.scraped_data)
    
    def _batch_analyze(self):
        """Analyze a batch of pages with a single AI call"""
        try:
            combined_content = "\n".join([page['content'] for page in self.page_batch])
            batch_analysis = self.ai_instance.analyze_content(
                combined_content,
                model=self.model,
                analysis_type="summary"  # Or strategy-based type
            )
            
            # Distribute results back to pages (simple split for demo)
            if batch_analysis and "content" in batch_analysis:
                summaries = batch_analysis["content"].split("\n---\n")  # Assume AI separates by --- 
                for i, page in enumerate(self.page_batch):
                    page["ai_summary"] = summaries[i] if i < len(summaries) else ""
            else:
                for page in self.page_batch:
                    page["ai_summary"] = page["content"][:200] + "..."
            
            # Add to main data
            self.scraped_data.extend(self.page_batch)
            
            # Async assess if needed
            for page in self.page_batch:
                self._assess_page_async(page)
        except Exception as e:
            self.logger.error(f"Batch analysis error: {str(e)}")
            # Fallback to individual
            for page in self.page_batch:
                try:
                    analysis = self.ai_instance.analyze_content(page["content"], self.model, "summary")
                    page["ai_summary"] = analysis.get("content", "")
                except:
                    page["ai_summary"] = page["content"][:200] + "..."
                self.scraped_data.append(page)
    
    def _fetch_page(self, url):
        """Fetch a single page and extract basic information"""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            headers = {
                'User-Agent': 'SuperCrawler-AI/1.0 (intelligent crawling)'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract basic page information
            title = soup.find('title').get_text().strip() if soup.find('title') else 'No Title'
            
            # Extract main content (prioritize article, main, or content divs)
            content_selectors = ['article', 'main', '[role="main"]', '.content', '.post-content', '.entry-content']
            content = ""
            
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    content = content_elem.get_text().strip()
                    break
            
            # Fallback to body content if no specific content area found
            if not content:
                content = soup.get_text().strip()
            
            return {
                'url': url,
                'title': title,
                'content': content[:5000],  # Limit content for processing
                'soup': soup,
                'word_count': len(content.split()),
                'scraped_at': str(datetime.now())
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching {url}: {str(e)}")
            return None
    
    def _extract_links(self, soup, current_url):
        """Extract and normalize links from the page"""
        try:
            import urllib.parse
            
            links = []
            base_domain = urllib.parse.urlparse(current_url).netloc
            
            for link_elem in soup.find_all('a', href=True):
                href = link_elem['href'].strip()
                
                # Skip empty, anchor, or javascript links
                if not href or href.startswith('#') or href.startswith('javascript:'):
                    continue
                
                # Convert relative URLs to absolute
                absolute_url = urllib.parse.urljoin(current_url, href)
                
                # Apply domain restriction if enabled
                if self.stay_domain:
                    link_domain = urllib.parse.urlparse(absolute_url).netloc
                    if link_domain != base_domain:
                        continue
                
                # Skip common file types and unwanted patterns
                unwanted_patterns = ['.pdf', '.jpg', '.png', '.gif', '.zip', '.exe', 'mailto:', 'tel:']
                if any(pattern in absolute_url.lower() for pattern in unwanted_patterns):
                    continue
                
                links.append(absolute_url)
            
            return list(set(links))  # Remove duplicates
            
        except Exception as e:
            self.logger.error(f"Error extracting links: {str(e)}")
            return []
    
    def _assess_page_async(self, page_data):
        """Assess page quality asynchronously"""
        def assessment_callback(quality_result):
            # Add quality assessment to page data
            page_data.update(quality_result)
            
            # Only keep high-quality, relevant pages
            min_quality = 4 if self.strategy.get('intelligence_level', 3) >= 4 else 3
            
            if quality_result.get('relevant', True) and quality_result.get('score', 5) >= min_quality:
                self.scraped_data.append(page_data)
                decision_msg = f"✅ Accepted: '{page_data.get('title', 'Unknown')[:50]}...' (Quality: {quality_result.get('score', 'N/A')}/10)"
                self.intelligent_decision.emit(decision_msg)
            else:
                decision_msg = f"❌ Filtered: '{page_data.get('title', 'Unknown')[:50]}...' (Quality: {quality_result.get('score', 'N/A')}/10 - {quality_result.get('reasoning', 'Low relevance')})"
                self.intelligent_decision.emit(decision_msg)
            
            # Remove from pending assessments
            if page_data in self.pending_assessments:
                self.pending_assessments.remove(page_data)
        
        # Add to pending assessments
        self.pending_assessments.append(page_data)
        
        # Start async assessment
        self.ai_instance.assess_page_quality_async(page_data, self.strategy, assessment_callback)
    
    def _wait_for_assessments(self):
        """Wait for pending quality assessments to complete"""
        import time
        wait_time = 0
        max_wait = 30  # Maximum 30 seconds wait
        
        while self.pending_assessments and wait_time < max_wait:
            time.sleep(0.5)
            wait_time += 0.5
        
        if self.pending_assessments:
            self.logger.warning(f"Timeout waiting for {len(self.pending_assessments)} assessments")
            # Add remaining pages without assessment
            for page_data in self.pending_assessments:
                page_data.update({
                    'score': 5,
                    'reasoning': 'Assessment timeout - included by default',
                    'relevant': True
                })
                self.scraped_data.append(page_data)
    
    def stop(self):
        """Stop the enhanced AI agent"""
        self.should_stop = True 