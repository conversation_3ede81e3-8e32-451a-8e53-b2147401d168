import requests
from bs4 import BeautifulSoup
import urllib.parse
import time
import random

import threading
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options

from PyQt5.QtCore import QObject, pyqtSignal
import os
import json
from datetime import datetime
from intellicrawler.utils.logger import get_logger
import jsonlines  # Add at top if not present
from intellicrawler.utils.browser_utils import find_browser_binary  # Add import
from intellicrawler.proxy_scraper_simple import ProxyScraper  # Add import

class Crawler(QObject):
    """Web crawler component that handles the scraping logic"""
    
    # Signals for UI updates
    progress_updated = pyqtSignal(int, int)  # current, total
    status_updated = pyqtSignal(str)
    page_scraped = pyqtSignal(dict)
    crawl_completed = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        self.driver = None  # Will be initialized lazily
        self.skip_retries = False  # New config flag, set via settings
        self.visited_urls = set()
        self.scraped_data = []
        self.base_url = ""
        self.should_stop = False
        
        # Thread-safe data structures
        self.visited_urls_lock = threading.Lock()
        self.scraped_data_lock = threading.Lock()
        self.pages_scraped_count = 0
        self.pages_scraped_lock = threading.Lock()
        
        self.base_delay = 1.0  # Initial base delay
        self.current_backoff = 0.5  # Starting backoff for failures
        self.failure_count = 0  # Track consecutive failures
        self.response_times = []  # For average calculation
        self.max_backoff = 5.0  # Maximum backoff
        self.batch_size = 10  # For disk streaming
        self.temp_file = 'temp_scraped.jsonl'  # Temp file for streaming
        self.proxy_scraper = ProxyScraper()
        self.proxies = []
        self.last_progress = 0
        self.progress_throttle = 5
    
    def initialize_selenium(self, chrome_path=None):
        """Initialize Selenium lazily if not already done"""
        if self.driver is not None:
            return True  # Already initialized
            
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920x1080")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-features=NetworkService")
            chrome_options.add_argument("--disable-features=IsolateOrigins")
            chrome_options.add_argument("--disable-site-isolation-trials")
            
            # Use utility to find binary
            binary_path = find_browser_binary(chrome_path)
            if not binary_path:
                self.logger.warning("Browser binary not found")
                self.error_occurred.emit("Browser binary not found. Please install Chrome or specify path.")
                return False
            
            is_edge = 'edge' in binary_path.lower() or 'msedge' in binary_path.lower()
            chrome_options.binary_location = binary_path
            
            if is_edge:
                # For Selenium 4.9.0, we need to use the WebDriver module directly
                from selenium.webdriver.edge.service import Service as EdgeService
                from webdriver_manager.microsoft import EdgeChromiumDriverManager
                
                # Create Edge options
                edge_options = webdriver.EdgeOptions()
                
                # Copy Chrome options to Edge options
                for arg in chrome_options.arguments:
                    edge_options.add_argument(arg)
                    
                edge_options.binary_location = binary_path
                
                # Create service with explicit timeouts
                edge_service = EdgeService(
                    EdgeChromiumDriverManager().install()
                )
                
                # Initialize Edge WebDriver with Selenium 4.9.0
                self.driver = webdriver.Edge(service=edge_service, options=edge_options)
                self.logger.info("Edge WebDriver initialized successfully with Selenium 4.9.0")
                return True
            else:
                # Try to find Chrome in common installation locations if path not provided
                if not chrome_path or not os.path.exists(chrome_path):
                    common_chrome_locations = [
                        # Windows default installation paths
                        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                        # User profile installation paths
                        os.path.join(os.environ.get('LOCALAPPDATA', ''), r"Google\Chrome\Application\chrome.exe"),
                        # Edge as fallback (Chromium-based)
                        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
                    ]
                    
                    for location in common_chrome_locations:
                        if os.path.exists(location):
                            self.logger.info(f"Found Chrome/Chromium browser at: {location}")
                            chrome_options.binary_location = location
                            
                            # Check if this is Edge browser
                            if "edge" in location.lower() or "msedge" in location.lower():
                                self.logger.info("Microsoft Edge detected, using Edge WebDriver")
                                try:
                                    # For Selenium 4.9.0, we need to use the WebDriver module directly
                                    from selenium.webdriver.edge.service import Service as EdgeService
                                    from webdriver_manager.microsoft import EdgeChromiumDriverManager
                                    
                                    # Create Edge options
                                    edge_options = webdriver.EdgeOptions()
                                    
                                    # Copy Chrome options to Edge options
                                    for arg in chrome_options.arguments:
                                        edge_options.add_argument(arg)
                                        
                                    edge_options.binary_location = location
                                    
                                    # Create service with explicit timeouts
                                    edge_service = EdgeService(
                                        EdgeChromiumDriverManager().install()
                                    )
                                    
                                    # Initialize Edge WebDriver with Selenium 4.9.0
                                    self.driver = webdriver.Edge(service=edge_service, options=edge_options)
                                    self.logger.info("Edge WebDriver initialized successfully with Selenium 4.9.0")
                                    return True
                                    
                                except Exception as e:
                                    self.logger.error(f"Error initializing Edge WebDriver: {str(e)}")
                                    raise
                            break
                    else:
                        # This only executes if the for loop completes without a break
                        self.logger.warning("Chrome browser not found in any common location")
                        helpful_message = (
                            "Chrome browser not found. Please do one of the following:\n"
                            "1. Install Google Chrome from https://www.google.com/chrome/\n"
                            "2. Specify the chrome_path parameter with the path to your Chrome executable\n"
                            "3. Use --no-dynamic option to disable JavaScript rendering (some websites may not work properly)"
                        )
                        self.logger.warning(helpful_message)
                        self.error_occurred.emit(helpful_message)
                        return False
                
                # If we're here, we're using Chrome (not Edge)
                # Add exception handling and retries for Chrome WebDriver installation
                max_retries = 3 if not self.skip_retries else 1
                retry_count = 0
                last_exception = None
                
                while retry_count < max_retries:
                    try:
                        # Create a Service with Selenium 4.9.0 compatible params
                        service = Service(ChromeDriverManager().install())
                        self.driver = webdriver.Chrome(service=service, options=chrome_options)
                        self.logger.info("Chrome WebDriver initialized successfully")
                        return True
                    except Exception as e:
                        retry_count += 1
                        last_exception = e
                        self.logger.warning(f"Chrome WebDriver initialization attempt {retry_count} failed: {str(e)}")
                        time.sleep(2)  # Wait before retrying
                
                # If we've exhausted all retries
                if last_exception:
                    raise last_exception
                    
        except Exception as e:
            error_message = f"Failed to initialize Selenium: {str(e)}"
            self.logger.error(error_message)
            self.error_occurred.emit(error_message)
            return False
    
    def reset_driver(self):
        """Reset the driver session without full reinitialization"""
        if self.driver:
            try:
                self.driver.delete_all_cookies()
                self.driver.execute_script("window.localStorage.clear();")
                self.driver.execute_script("window.sessionStorage.clear();")
                self.driver.refresh()
            except Exception as e:
                self.logger.error(f"Error resetting driver: {str(e)}")
                self.driver.quit()
                self.driver = None
                self.initialize_selenium()  # Reinitialize if reset fails
    
    def crawl(self, start_url, max_pages=100, depth=3, dynamic=False, delay=1.0, chrome_path=None):
        """
        Main crawling method
        
        Args:
            start_url (str): URL to start crawling from
            max_pages (int): Maximum number of pages to crawl
            depth (int): Maximum crawl depth
            dynamic (bool): Whether to use Selenium for dynamic content
            delay (float): Delay between requests in seconds
            chrome_path (str, optional): Path to Chrome binary
        """
        self.should_stop = False
        self.visited_urls = set()
        self.scraped_data = []  # Will hold minimal data or references
        # Get fresh proxies using the correct method name
        try:
            def progress_callback(percent, message):
                self.status_updated.emit(f"Loading proxies: {message}")
            self.proxies = self.proxy_scraper.scrape_all_sources(progress_callback)
            self.logger.info(f"Loaded {len(self.proxies)} proxies for crawling")
        except Exception as e:
            self.logger.warning(f"Failed to load proxies: {str(e)}. Continuing without proxies.")
            self.proxies = []
        with jsonlines.open(self.temp_file, mode='w') as writer:
            try:
                self.base_url = urllib.parse.urlparse(start_url).netloc
                
                if dynamic:
                    # Initialize Selenium if it's not already initialized
                    if self.driver is None:
                        selenium_init_success = self.initialize_selenium(chrome_path)
                        if not selenium_init_success:
                            error_message = "Failed to initialize Selenium WebDriver. Aborting crawl."
                            self.logger.error(error_message)
                            self.error_occurred.emit(error_message)
                            return
                    else:
                        self.reset_driver()  # Reset for new crawl
                
                self.status_updated.emit(f"Starting crawl from {start_url}")
                
                # URLs to crawl with their depth
                urls_to_crawl = [(start_url, 0)]  
                
                pages_scraped = 0
                
                while urls_to_crawl and pages_scraped < max_pages and not self.should_stop:
                    url, current_depth = urls_to_crawl.pop(0)
                    
                    if url in self.visited_urls:
                        continue
                        
                    self.visited_urls.add(url)
                    
                    try:
                        self.status_updated.emit(f"Scraping {url}")
                        
                        # Select random proxy
                        if self.proxies:
                            proxy = random.choice(self.proxies)
                            self.session.proxies = {'http': proxy, 'https': proxy}
                        
                        # Get page content
                        if dynamic:
                            # Verify that driver is available
                            if self.driver is None:
                                error_message = "WebDriver is not initialized. Aborting crawl."
                                self.logger.error(error_message)
                                self.error_occurred.emit(error_message)
                                return
                                
                            try:
                                self.driver.get(url)
                                time.sleep(delay)  # Wait for JS to load
                                page_content = self.driver.page_source
                            except Exception as e:
                                error_message = f"Error loading page with Selenium: {str(e)}"
                                self.logger.error(error_message)
                                self.error_occurred.emit(error_message)
                                continue  # Skip this URL and continue with the next
                        else:
                            try:
                                response = self.session.get(url, timeout=10)
                                if response.status_code != 200:
                                    self.logger.warning(f"Received status code {response.status_code} for {url}")
                                    continue
                                page_content = response.text
                            except Exception as e:
                                error_message = f"Error fetching page {url}: {str(e)}"
                                self.logger.error(error_message)
                                self.error_occurred.emit(error_message)
                                continue  # Skip this URL and continue with the next
                        
                        # Parse the page
                        try:
                            soup = BeautifulSoup(page_content, 'lxml')
                        except Exception:
                            # Fallback to html.parser if lxml is not available
                            soup = BeautifulSoup(page_content, 'html.parser')
                        
                        # Extract page data
                        title = soup.title.string if soup.title else "No Title"
                        text_content = soup.get_text(separator=' ', strip=True)
                        
                        # Store the scraped data
                        page_data = {
                            'url': url,
                            'title': title,
                            'content': text_content,
                            'html': page_content,
                            'timestamp': datetime.now().isoformat(),
                            'depth': current_depth
                        }
                        
                        # Stream to disk
                        writer.write(page_data)
                        
                        # Keep only reference in memory (e.g., url and summary)
                        self.scraped_data.append({'url': url, 'title': title})
                        self.page_scraped.emit({'url': url, 'title': title})  # Emit minimal
                        pages_scraped += 1
                        
                        # Throttled progress update
                        if pages_scraped - self.last_progress >= self.progress_throttle or pages_scraped == max_pages:
                            self.progress_updated.emit(pages_scraped, max_pages)
                            self.last_progress = pages_scraped
                        
                        # If we haven't reached max depth, collect more URLs
                        if current_depth < depth:
                            new_urls = self._extract_links(soup, url)
                            # Add new URLs with incremented depth
                            for new_url in new_urls:
                                if new_url not in self.visited_urls:
                                    urls_to_crawl.append((new_url, current_depth + 1))
                        
                        # Adaptive delay
                        start_time = time.time()  # Track response time
                        avg_resp = sum(self.response_times) / len(self.response_times) if self.response_times else 1.0
                        adjusted_delay = max(0.1, self.base_delay * (avg_resp / 1.0))  # Scale based on avg response
                        time.sleep(adjusted_delay + random.uniform(0.1, 0.5))
                        
                    except Exception as e:
                        self.failure_count += 1
                        backoff_delay = min(self.current_backoff * (2 ** self.failure_count), self.max_backoff)
                        time.sleep(backoff_delay)
                        self.current_backoff = backoff_delay  # Update for next potential failure
                        self.logger.error(f"Error crawling {url}: {str(e)}")
                        self.error_occurred.emit(f"Error crawling {url}: {str(e)}")
                
                self.status_updated.emit(f"Crawl completed. Scraped {pages_scraped} pages.")
                self.crawl_completed.emit(self.scraped_data)  # Emit minimal data
                # Note: Full data is in temp_file
                
                # Clean up Selenium if used
                if dynamic and self.driver:
                    self.driver.quit()
                    self.driver = None
                
            except Exception as e:
                error_message = f"Error in crawl method: {str(e)}"
                self.logger.error(error_message)
                self.error_occurred.emit(error_message)
    
    def stop(self):
        """Stop the ongoing crawl"""
        self.should_stop = True
        self.status_updated.emit("Crawl stopping...")
        
        # Safely quit the WebDriver if it exists
        try:
            if hasattr(self, 'driver') and self.driver is not None:
                self.logger.info("Closing WebDriver...")
                try:
                    self.driver.quit()
                except Exception as e:
                    self.logger.error(f"Error while closing WebDriver: {str(e)}")
                finally:
                    self.driver = None
        except Exception as e:
            self.logger.error(f"Error in stop method: {str(e)}")
    
    def _extract_links(self, soup, current_url):
        """Extract links from the page that belong to the same domain"""
        parsed_url = urllib.parse.urlparse(current_url)
        base_url = parsed_url.netloc

        links = set()  # Use set to avoid duplicates

        # Extract from <a> tags
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href'].strip()

            # Skip empty, anchor, or javascript links
            if not href or href.startswith('#') or href.startswith('javascript:') or href.startswith('mailto:') or href.startswith('tel:'):
                continue

            # Handle relative URLs
            if href.startswith('/'):
                href = f"{parsed_url.scheme}://{base_url}{href}"
            elif not href.startswith(('http://', 'https://')):
                # Resolve relative link
                href = urllib.parse.urljoin(current_url, href)

            # Only follow links in the same domain
            parsed_href = urllib.parse.urlparse(href)
            if parsed_href.netloc == base_url:
                # Clean up the URL (remove fragments, normalize)
                clean_url = f"{parsed_href.scheme}://{parsed_href.netloc}{parsed_href.path}"
                if parsed_href.query:
                    clean_url += f"?{parsed_href.query}"
                links.add(clean_url)

        # Also extract from area tags (image maps)
        for area_tag in soup.find_all('area', href=True):
            href = area_tag['href'].strip()
            if href and not href.startswith(('#', 'javascript:', 'mailto:', 'tel:')):
                if href.startswith('/'):
                    href = f"{parsed_url.scheme}://{base_url}{href}"
                elif not href.startswith(('http://', 'https://')):
                    href = urllib.parse.urljoin(current_url, href)

                parsed_href = urllib.parse.urlparse(href)
                if parsed_href.netloc == base_url:
                    clean_url = f"{parsed_href.scheme}://{parsed_href.netloc}{parsed_href.path}"
                    if parsed_href.query:
                        clean_url += f"?{parsed_href.query}"
                    links.add(clean_url)

        self.logger.info(f"Extracted {len(links)} unique links from {current_url}")
        return list(links)
    
    def save_scraped_data(self, filepath):
        """Save from temp JSONL to final JSON"""
        try:
            data = []
            with jsonlines.open(self.temp_file, mode='r') as reader:
                for obj in reader:
                    data.append(obj)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            os.remove(self.temp_file)  # Clean up
            return True
        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")
            self.error_occurred.emit(f"Error saving data: {str(e)}")
            return False
    
    def scrape_page(self, url, timeout=10):
        """
        Scrape a single page and return its content
        
        Args:
            url (str): URL to scrape
            timeout (int): Request timeout in seconds
            
        Returns:
            dict: Page data with title, content, url, and timestamp
        """
        try:
            self.logger.info(f"Scraping single page: {url}")
            
            # Use requests session for single page scraping
            response = self.session.get(url, timeout=timeout)
            if response.status_code != 200:
                self.logger.warning(f"Received status code {response.status_code} for {url}")
                return None
                
            page_content = response.text
            
            # Parse the page
            try:
                soup = BeautifulSoup(page_content, 'lxml')
            except Exception:
                # Fallback to html.parser if lxml is not available
                soup = BeautifulSoup(page_content, 'html.parser')
            
            # Extract page data
            title = soup.title.string if soup.title else "No Title"
            text_content = soup.get_text(separator=' ', strip=True)
            
            # Return page data
            page_data = {
                'url': url,
                'title': title.strip() if title else "No Title",
                'content': text_content,
                'html': page_content,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"Successfully scraped page: {url} ({len(text_content)} characters)")
            return page_data
            
        except Exception as e:
            self.logger.error(f"Error scraping page {url}: {str(e)}")
            return None 