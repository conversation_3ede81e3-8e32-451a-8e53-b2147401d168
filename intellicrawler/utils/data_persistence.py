#!/usr/bin/env python3
"""
Data Persistence Module for IntelliCrawler
Handles automatic saving, loading, and session recovery of crawled data.
"""

import os
import json
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from intellicrawler.utils.logger import get_logger

class DataPersistenceManager:
    """Manages automatic data persistence and session recovery for IntelliCrawler"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Set up persistence directories
        self.base_dir = os.path.join(os.path.expanduser("~"), "IntelliCrawler_Data")
        self.sessions_dir = os.path.join(self.base_dir, "sessions")
        self.exports_dir = os.path.join(self.base_dir, "exports")
        self.backups_dir = os.path.join(self.base_dir, "backups")
        
        # Create directories if they don't exist
        for directory in [self.base_dir, self.sessions_dir, self.exports_dir, self.backups_dir]:
            os.makedirs(directory, exist_ok=True)
            
        self.logger.info(f"Data persistence initialized: {self.base_dir}")
        
    def save_session_data(self, data: List[Dict[str, Any]], session_id: str = None) -> str:
        """
        Save crawled data as a session file for automatic recovery
        
        Args:
            data: List of crawled page data
            session_id: Optional session identifier, auto-generated if not provided
            
        Returns:
            str: Session ID for the saved data
        """
        try:
            if not session_id:
                session_id = datetime.now().strftime("session_%Y%m%d_%H%M%S")

            # Clean data to ensure JSON serializability (remove BeautifulSoup objects)
            cleaned_data = []
            if data:
                for item in data:
                    if isinstance(item, dict):
                        cleaned_item = {}
                        for key, value in item.items():
                            # Skip BeautifulSoup objects and other non-serializable objects
                            if key == 'soup' or str(type(value)).startswith('<class \'bs4.'):
                                continue
                            # Convert any remaining complex objects to strings
                            try:
                                json.dumps(value)  # Test if serializable
                                cleaned_item[key] = value
                            except (TypeError, ValueError):
                                cleaned_item[key] = str(value)
                        cleaned_data.append(cleaned_item)
                    else:
                        # For non-dict items, convert to string if not serializable
                        try:
                            json.dumps(item)
                            cleaned_data.append(item)
                        except (TypeError, ValueError):
                            cleaned_data.append(str(item))

            session_data = {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "data_count": len(cleaned_data),
                "crawled_data": cleaned_data,
                "metadata": {
                    "version": "2.0.0",
                    "application": "IntelliCrawler",
                    "auto_saved": True
                }
            }
            
            session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
            
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Session data saved: {session_file} ({len(data)} pages)")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Failed to save session data: {str(e)}")
            raise
    
    def load_session_data(self, session_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Load crawled data from a session file
        
        Args:
            session_id: Session identifier
            
        Returns:
            List of crawled page data or None if not found
        """
        try:
            session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
            
            if not os.path.exists(session_file):
                self.logger.warning(f"Session file not found: {session_file}")
                return None
            
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            crawled_data = session_data.get('crawled_data', [])
            self.logger.info(f"Session data loaded: {session_id} ({len(crawled_data)} pages)")
            
            return crawled_data
            
        except Exception as e:
            self.logger.error(f"Failed to load session data: {str(e)}")
            return None
    
    def get_recent_sessions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get list of recent session files
        
        Args:
            limit: Maximum number of sessions to return
            
        Returns:
            List of session info dictionaries
        """
        try:
            sessions = []
            
            for filename in os.listdir(self.sessions_dir):
                if filename.endswith('.json'):
                    session_file = os.path.join(self.sessions_dir, filename)
                    
                    try:
                        with open(session_file, 'r', encoding='utf-8') as f:
                            session_data = json.load(f)
                        
                        sessions.append({
                            "session_id": session_data.get('session_id', filename[:-5]),
                            "timestamp": session_data.get('timestamp', ''),
                            "data_count": session_data.get('data_count', 0),
                            "file_path": session_file,
                            "file_size": os.path.getsize(session_file)
                        })
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to read session file {filename}: {str(e)}")
                        continue
            
            # Sort by timestamp (newest first)
            sessions.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return sessions[:limit]
            
        except Exception as e:
            self.logger.error(f"Failed to get recent sessions: {str(e)}")
            return []
    
    def export_session_data(self, session_id: str, export_format: str = "json") -> Optional[str]:
        """
        Export session data to the exports directory
        
        Args:
            session_id: Session identifier
            export_format: Export format (json, csv, markdown)
            
        Returns:
            Path to exported file or None if failed
        """
        try:
            data = self.load_session_data(session_id)
            if not data:
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if export_format == "json":
                export_file = os.path.join(self.exports_dir, f"crawl_data_{timestamp}.json")
                
                export_data = {
                    "export_info": {
                        "export_date": datetime.now().isoformat(),
                        "total_pages": len(data),
                        "crawler_version": "2.0.0",
                        "source_session": session_id
                    },
                    "crawled_data": data
                }
                
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)
                    
            elif export_format == "markdown":
                export_file = os.path.join(self.exports_dir, f"crawl_report_{timestamp}.md")
                
                with open(export_file, 'w', encoding='utf-8') as f:
                    f.write(f"# IntelliCrawler Report\n\n")
                    f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**Total Pages:** {len(data)}\n")
                    f.write(f"**Source Session:** {session_id}\n\n")
                    f.write("---\n\n")
                    
                    for i, item in enumerate(data, 1):
                        f.write(f"## Page {i}: {item.get('title', 'Untitled')}\n\n")
                        f.write(f"**URL:** {item.get('url', 'Unknown')}\n\n")
                        f.write(f"{item.get('content', 'No content available')}\n\n")
                        f.write("---\n\n")
            
            else:
                self.logger.error(f"Unsupported export format: {export_format}")
                return None
            
            self.logger.info(f"Session exported: {export_file}")
            return export_file
            
        except Exception as e:
            self.logger.error(f"Failed to export session data: {str(e)}")
            return None
    
    def cleanup_old_sessions(self, days_to_keep: int = 30) -> int:
        """
        Clean up old session files
        
        Args:
            days_to_keep: Number of days to keep session files
            
        Returns:
            Number of files cleaned up
        """
        try:
            import time
            
            cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
            cleaned_count = 0
            
            for filename in os.listdir(self.sessions_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.sessions_dir, filename)
                    
                    if os.path.getmtime(file_path) < cutoff_time:
                        # Move to backup before deleting
                        backup_path = os.path.join(self.backups_dir, filename)
                        shutil.move(file_path, backup_path)
                        cleaned_count += 1
            
            self.logger.info(f"Cleaned up {cleaned_count} old session files")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old sessions: {str(e)}")
            return 0
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get information about storage usage"""
        try:
            def get_dir_size(directory):
                total_size = 0
                file_count = 0
                
                for dirpath, dirnames, filenames in os.walk(directory):
                    for filename in filenames:
                        file_path = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(file_path)
                        file_count += 1
                
                return total_size, file_count
            
            sessions_size, sessions_count = get_dir_size(self.sessions_dir)
            exports_size, exports_count = get_dir_size(self.exports_dir)
            backups_size, backups_count = get_dir_size(self.backups_dir)
            
            return {
                "base_directory": self.base_dir,
                "sessions": {
                    "count": sessions_count,
                    "size_bytes": sessions_size,
                    "size_mb": round(sessions_size / (1024 * 1024), 2)
                },
                "exports": {
                    "count": exports_count,
                    "size_bytes": exports_size,
                    "size_mb": round(exports_size / (1024 * 1024), 2)
                },
                "backups": {
                    "count": backups_count,
                    "size_bytes": backups_size,
                    "size_mb": round(backups_size / (1024 * 1024), 2)
                },
                "total_size_mb": round((sessions_size + exports_size + backups_size) / (1024 * 1024), 2)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get storage info: {str(e)}")
            return {}
