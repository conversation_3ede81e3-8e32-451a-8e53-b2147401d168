"""
SuperCrawler - Error Handling Module

This module provides specialized error handling functionality for SuperCrawler,
including UI error displays, error collection, and reporting.
"""

import sys
import os
import traceback
import logging
import functools
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox
from intellicrawler.utils.logger import get_logger

# Store all uncaught exceptions for the current session
_session_errors = []

logger = get_logger(__name__)

class ErrorHandler:
    """Error handling and management class for SuperCrawler"""
    
    @staticmethod
    def handle_exception(error_type, error_value, error_traceback):
        """
        Global exception handler for uncaught exceptions
        
        Args:
            error_type: Type of the exception
            error_value: Exception object
            error_traceback: Traceback object
        """
        # Format the traceback
        tb_lines = traceback.format_exception(error_type, error_value, error_traceback)
        tb_text = ''.join(tb_lines)
        
        # Generate an error ID and log the error
        error_id = ErrorHandler.log_error(error_value, "Uncaught exception")
        
        # Store error in session errors
        timestamp = datetime.now().isoformat()
        _session_errors.append({
            'error_id': error_id,
            'timestamp': timestamp,
            'type': str(error_type.__name__),
            'message': str(error_value),
            'traceback': tb_text
        })
        
        # Print error to stderr
        print(f"\nUnhandled exception [{error_id}]:", file=sys.stderr)
        print(tb_text, file=sys.stderr)
        
        # Note: Can't show QMessageBox here as it might not be in the main thread
        # or if the application is not initialized yet
        
    @staticmethod
    def setup_exception_handling():
        """Set up global exception handling"""
        # Set the global exception handler
        sys.excepthook = ErrorHandler.handle_exception
        
        # Make sure exceptions in slots are not lost
        # Note: This only works for PyQt5 5.5+
        try:
            from PyQt5.QtCore import QCoreApplication
            QCoreApplication.instance().setAttribute(
                QCoreApplication.setAttribute.Qt.AA_UseHighDpiPixmaps, True
            )
        except (ImportError, AttributeError):
            pass  # Skip if PyQt5 version doesn't support it
    
    @staticmethod
    def log_error(error, context=None):
        """
        Log an error with context information
        
        Args:
            error: The exception to log
            context: Additional context information (optional)
        """
        error_id = datetime.now().strftime("ERR-%Y%m%d%H%M%S")
        
        # Format traceback
        exc_type, exc_value, exc_traceback = sys.exc_info()
        tb_str = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # Log with context if provided
        if context:
            logger.error(f"[{error_id}] {context}: {str(error)}\n{tb_str}")
        else:
            logger.error(f"[{error_id}] {str(error)}\n{tb_str}")
            
        # Create error report file
        error_dir = os.path.expanduser("~/.supercrawler/logs/errors")
        os.makedirs(error_dir, exist_ok=True)
        
        error_file = os.path.join(error_dir, f"{error_id}.log")
        with open(error_file, 'w') as f:
            f.write(f"Error ID: {error_id}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            if context:
                f.write(f"Context: {context}\n")
            f.write(f"Error: {str(error)}\n\n")
            f.write("Traceback:\n")
            f.write(tb_str)
            
        return error_id
    
    @staticmethod
    def show_error_dialog(parent, title, message, detailed_text=None):
        """
        Show an error dialog with optional details
        
        Args:
            parent: Parent widget
            title: Dialog title
            message: Error message
            detailed_text: Detailed error information (optional)
        """
        msg_box = QMessageBox(parent)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        
        if detailed_text:
            msg_box.setDetailedText(detailed_text)
            
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    @staticmethod
    def get_session_errors():
        """
        Get all errors that occurred in the current session
        
        Returns:
            list: List of error dictionaries
        """
        return _session_errors.copy()
    
    @staticmethod
    def export_session_errors(filepath=None):
        """
        Export all session errors to a file
        
        Args:
            filepath (str, optional): Path to save the error report. If None,
                                      saves to a default location.
        
        Returns:
            str: Path to the saved error report
        """
        import json
        
        if not filepath:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_dir = os.path.expanduser("~/.supercrawler/logs/error_reports")
            os.makedirs(log_dir, exist_ok=True)
            filepath = os.path.join(log_dir, f"error_report_{timestamp}.json")
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(_session_errors, f, indent=2)
            
            logger.info(f"Exported error report to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to export error report: {str(e)}")
            return None

def try_except_with_dialog(func):
    """
    Decorator that catches exceptions and shows an error dialog
    
    Args:
        func: The function to wrap
        
    Returns:
        Wrapped function with error handling
    """
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except Exception as e:
            # Log the error
            error_id = ErrorHandler.log_error(e, f"Error in {func.__name__}")
            
            # Show error dialog
            ErrorHandler.show_error_dialog(
                self,
                "Error",
                f"An error occurred: {str(e)}",
                f"Error ID: {error_id}\nCheck the log file for details."
            )
            return None  # Return None to prevent further errors
            
    return wrapper 