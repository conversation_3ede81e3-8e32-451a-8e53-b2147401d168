# IntelliCrawler Dependencies

# Core web scraping
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
selenium>=4.15.0
webdriver-manager>=4.0.0

# GUI Framework
PyQt5>=5.15.0

# AI Integration
openai>=1.0.0  # For DeepSeek API compatibility

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Text Processing
nltk>=3.8.0

# Code Display & Syntax Highlighting
pygments>=2.15.0

# Progress Bars
tqdm>=4.65.0

# Configuration
python-dotenv>=1.0.0

# Windows Integration (Windows only)
pywin32>=306; sys_platform == "win32"
winshell>=0.6; sys_platform == "win32"

# Audio Processing (Optional)
librosa>=0.10.0; extra == "audio"
pygame>=2.5.0; extra == "audio"

# Development Dependencies (Optional)
pytest>=7.4.0; extra == "dev"
pytest-cov>=4.1.0; extra == "dev"
pytest-qt>=4.2.0; extra == "dev"
ruff>=0.1.0; extra == "dev"
mypy>=1.5.0; extra == "dev"
vulture>=2.9.0; extra == "dev"
bandit>=1.7.0; extra == "dev"
safety>=2.3.0; extra == "dev" 